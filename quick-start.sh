#!/bin/bash

# Quick Start Script for Enhanced Black-G CLI
# Simplified version that focuses on essential checks and fast startup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check essential requirements
check_essentials() {
    print_header "🚀 Quick System Check..."
    
    local all_good=true
    
    # Check Node.js
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version)
        print_success "Node.js: $node_version"
    else
        print_error "Node.js is not installed"
        all_good=false
    fi
    
    # Check npm
    if command -v npm >/dev/null 2>&1; then
        local npm_version=$(npm --version)
        print_success "npm: v$npm_version"
    else
        print_error "npm is not installed"
        all_good=false
    fi
    
    # Check .env file
    if [ -f "$SCRIPT_DIR/.env" ]; then
        if grep -q "GEMINI_API_KEY=" "$SCRIPT_DIR/.env"; then
            local api_key=$(grep "GEMINI_API_KEY=" "$SCRIPT_DIR/.env" | cut -d'=' -f2)
            if [ -n "$api_key" ] && [ "$api_key" != "your_api_key_here" ]; then
                print_success "Gemini API key configured"
            else
                print_error "Gemini API key not properly configured"
                print_status "Please set GEMINI_API_KEY in .env file"
                all_good=false
            fi
        else
            print_error "GEMINI_API_KEY not found in .env file"
            all_good=false
        fi
    else
        print_error ".env file not found"
        print_status "Creating .env template..."
        cat > "$SCRIPT_DIR/.env" << EOF
# Enhanced Black-G CLI Configuration
GEMINI_API_KEY=your_api_key_here
BLACK_G_LOG_LEVEL=info
EOF
        print_status "Please edit .env file and set your Gemini API key"
        all_good=false
    fi
    
    # Check node_modules
    if [ -d "$SCRIPT_DIR/node_modules" ]; then
        print_success "Dependencies installed"
    else
        print_warning "Installing dependencies..."
        cd "$SCRIPT_DIR"
        npm install --silent
        print_success "Dependencies installed"
    fi
    
    if [ "$all_good" = false ]; then
        print_error "Some requirements are missing. Please fix them before continuing."
        exit 1
    fi
    
    print_success "Essential checks passed!"
}

# Function to start the enhanced CLI
start_cli() {
    print_header "🚀 Starting Enhanced Black-G CLI..."
    
    cd "$SCRIPT_DIR"
    
    # Make sure the CLI is executable
    chmod +x enhanced-black-g-cli.js 2>/dev/null || true
    
    # Start the CLI
    print_status "Launching Enhanced Black-G CLI..."
    echo ""
    node enhanced-black-g-cli.js
}

# Function to show usage
show_usage() {
    echo "Enhanced Black-G CLI Quick Start Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --check-only    Only run essential checks, don't start CLI"
    echo "  --no-checks     Skip checks and start directly"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run checks and start CLI (default)"
    echo "  $0 --check-only # Only run essential checks"
    echo "  $0 --no-checks  # Start CLI without checks"
}

# Function to install missing tools
install_tools() {
    print_header "📦 Installing Missing Security Tools..."
    
    # Update package list
    print_status "Updating package list..."
    sudo apt update -qq
    
    # Install security tools
    print_status "Installing security tools..."
    sudo apt install -y nmap masscan sslscan dirb nikto whatweb
    
    # Install Go-based tools if Go is available
    if command -v go >/dev/null 2>&1; then
        print_status "Installing Go-based security tools..."
        
        # Set up Go environment
        export GOPATH="$HOME/go"
        export PATH="$PATH:$GOPATH/bin"
        
        # Create go bin directory if it doesn't exist
        mkdir -p "$GOPATH/bin"
        
        # Install tools
        print_status "Installing subfinder..."
        go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest 2>/dev/null || print_warning "Failed to install subfinder"
        
        print_status "Installing nuclei..."
        go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest 2>/dev/null || print_warning "Failed to install nuclei"
        
        print_status "Installing gobuster..."
        go install github.com/OJ/gobuster/v3@latest 2>/dev/null || print_warning "Failed to install gobuster"
        
        print_status "Installing amass..."
        go install -v github.com/OWASP/Amass/v3/...@master 2>/dev/null || print_warning "Failed to install amass"
        
        print_success "Go-based tools installation completed"
        
        # Add Go bin to PATH if not already there
        if [[ ":$PATH:" != *":$GOPATH/bin:"* ]]; then
            print_status "Adding Go bin directory to PATH..."
            echo 'export PATH="$HOME/go/bin:$PATH"' >> ~/.bashrc
            print_status "Please run 'source ~/.bashrc' or restart your terminal"
        fi
    else
        print_warning "Go not installed - skipping Go-based tools"
    fi
    
    print_success "Tool installation completed"
}

# Main execution
main() {
    # Parse command line arguments
    case "${1:-}" in
        --help)
            show_usage
            exit 0
            ;;
        --check-only)
            check_essentials
            print_success "Essential checks completed successfully"
            exit 0
            ;;
        --no-checks)
            print_status "Skipping checks and starting CLI directly..."
            start_cli
            ;;
        --install-tools)
            install_tools
            exit 0
            ;;
        "")
            # Default behavior
            check_essentials
            start_cli
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Trap Ctrl+C
trap 'print_status "Shutting down..."; exit 0' INT

# Show banner
echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════════════╗"
echo "║                    Enhanced Black-G CLI Quick Start                 ║"
echo "║                  Interactive AI Penetration Testing                 ║"
echo "╚══════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Run main function
main "$@"
