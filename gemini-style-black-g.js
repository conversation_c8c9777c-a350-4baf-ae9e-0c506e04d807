#!/usr/bin/env node

/**
 * Gemini-Style Black-G CLI
 * Interactive AI-Driven Penetration Testing with Gemini CLI Interface
 * 
 * Features:
 * - Real-time command execution boxes like Gemini CLI
 * - Live progress indicators and output streaming
 * - Multiple concurrent process monitoring
 * - Interactive AI integration with permission controls
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

// Import Gemini-style components
const GeminiStyleTerminal = require('./src/core/GeminiStyleTerminal');
const GeminiStyleProcessManager = require('./src/core/GeminiStyleProcessManager');
const ParrotOSToolManager = require('./src/core/ParrotOSToolManager');
const EnhancedLogger = require('./src/utils/enhanced-logger');

class GeminiStyleBlackG {
    constructor() {
        this.genAI = null;
        this.model = null;
        this.chatHistory = [];
        
        // Initialize Gemini-style components
        this.terminal = new GeminiStyleTerminal({
            enableColors: true,
            maxConcurrentBoxes: 5,
            boxHeight: 10,
            boxWidth: 120
        });
        
        this.processManager = new GeminiStyleProcessManager(this.terminal, {
            maxConcurrentProcesses: 3,
            progressUpdateInterval: 300
        });
        
        this.toolManager = new ParrotOSToolManager({
            enableAutoInstall: false,
            enablePathFix: true
        });
        
        this.logger = new EnhancedLogger({
            logLevel: process.env.BLACK_G_LOG_LEVEL || 'info',
            enableMetrics: true,
            enableAlerts: true,
            enableConsole: false // Disable console logging for clean interface
        });

        this.setupEventHandlers();
        this.initializeSystem();
    }

    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Terminal events
        this.terminal.on('input', (input) => this.handleUserInput(input));
        this.terminal.on('interrupt', () => this.handleInterrupt());

        // Process manager events
        this.processManager.on('processStarted', (processInfo) => {
            this.logger.info('Process started', { processId: processInfo.id, command: processInfo.command });
        });
        
        this.processManager.on('processCompleted', (processInfo) => {
            this.logger.info('Process completed', { processId: processInfo.id, duration: processInfo.duration });
        });

        // Tool manager events
        this.toolManager.on('initialized', (stats) => {
            this.terminal.showMessage(`Tool discovery: ${stats.availableTools}/${stats.availableTools + stats.missingTools} tools available`, 'success');
        });

        // Logger events
        this.logger.on('alert', (alert) => {
            this.terminal.showMessage(`Alert: ${alert.type}`, 'warning');
        });
    }

    /**
     * Initialize the system
     */
    async initializeSystem() {
        try {
            // Initialize AI
            await this.initializeAI();
            
            // Initialize tool manager (silently)
            await this.toolManager.initialize();
            
            // Setup chat
            this.initializeChat();
            
            // Start the terminal interface
            this.terminal.start();
            
            // Show welcome message
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error(`Failed to initialize Gemini-Style Black-G CLI: ${error.message}`);
            process.exit(1);
        }
    }

    /**
     * Initialize AI
     */
    async initializeAI() {
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            throw new Error('GEMINI_API_KEY environment variable not set');
        }
        
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
    }

    /**
     * Initialize chat
     */
    initializeChat() {
        const toolStats = this.toolManager.getStatistics();
        const availableTools = this.toolManager.getAvailableTools();
        
        const systemPrompt = `You are Black-G, an advanced AI penetration testing assistant with a Gemini CLI-style interface.

INTERFACE STYLE:
- Commands execute in real-time boxes with progress indicators
- Multiple concurrent processes with live output streaming
- Clean, organized terminal layout similar to Gemini CLI

AVAILABLE TOOLS: ${availableTools.join(', ')}
TOOL AVAILABILITY: ${toolStats.available}/${toolStats.total} tools (${toolStats.availabilityRate}%)

RESPONSE FORMAT:
- Provide clear, concise analysis
- Suggest specific commands for execution
- Include risk assessment and warnings
- Format for real-time execution display

SECURITY PROTOCOLS:
1. Always assess command risk (low/medium/high)
2. Provide clear warnings for aggressive scans
3. Suggest appropriate tool parameters
4. Emphasize proper authorization

Remember: You're assisting with authorized penetration testing. Always emphasize responsible disclosure and proper authorization.`;

        this.chatHistory = [
            {
                role: 'user',
                parts: [{ text: systemPrompt }]
            },
            {
                role: 'model',
                parts: [{ text: 'Gemini-Style Black-G AI Assistant ready. I can help you with interactive penetration testing using real-time command execution boxes. What security testing would you like to perform?' }]
            }
        ];
    }

    /**
     * Handle user input
     */
    async handleUserInput(input) {
        if (!input) return;

        try {
            // Log user interaction
            this.logger.info('User input received', { input, timestamp: new Date().toISOString() });
            
            // Let the process manager handle the input
            // It will determine if it's a built-in command, direct command, or AI request
            
        } catch (error) {
            this.terminal.showMessage(`Error: ${error.message}`, 'error');
            this.logger.error('Input processing failed', { input, error: error.message });
        }
    }

    /**
     * Process AI request
     */
    async processAIRequest(userInput) {
        const startTime = Date.now();
        
        this.terminal.showMessage('Processing AI request...', 'info');
        
        // Add user input to chat history
        this.chatHistory.push({
            role: 'user',
            parts: [{ text: userInput }]
        });

        try {
            // Get AI analysis
            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(userInput);
            const response = await result.response;
            const aiResponse = response.text();

            // Add AI response to chat history
            this.chatHistory.push({
                role: 'model',
                parts: [{ text: aiResponse }]
            });

            // Show AI response in a clean format
            this.displayAIResponse(aiResponse);
            
            // Log AI interaction
            const duration = Date.now() - startTime;
            this.logger.logAIInteraction(userInput, aiResponse, duration);
            
            return aiResponse;
            
        } catch (error) {
            this.terminal.showMessage(`AI processing failed: ${error.message}`, 'error');
            this.logger.error('AI processing failed', { userInput, error: error.message });
            throw error;
        }
    }

    /**
     * Display AI response
     */
    displayAIResponse(response) {
        // Parse and display the AI response in a clean format
        const lines = response.split('\n');
        
        console.log(`\n${this.terminal.colors.brightCyan}🤖 AI ANALYSIS${this.terminal.colors.reset}`);
        console.log('─'.repeat(80));
        
        lines.forEach(line => {
            if (line.trim()) {
                if (line.startsWith('**') && line.endsWith('**')) {
                    // Bold headers
                    console.log(`${this.terminal.colors.bright}${line.slice(2, -2)}${this.terminal.colors.reset}`);
                } else if (line.startsWith('- ')) {
                    // Bullet points
                    console.log(`${this.terminal.colors.brightYellow}${line}${this.terminal.colors.reset}`);
                } else {
                    console.log(line);
                }
            }
        });
        
        console.log('─'.repeat(80));
    }

    /**
     * Show welcome message
     */
    showWelcomeMessage() {
        setTimeout(() => {
            this.terminal.showMessage('Gemini-Style Black-G CLI ready!', 'success');
            this.terminal.showMessage('Type "help" for commands or "demo" for demonstration', 'info');
            this.terminal.showMessage('Try: "Find subdomains for example.com"', 'info');
        }, 1000);
    }

    /**
     * Handle interrupt
     */
    handleInterrupt() {
        this.terminal.showMessage('Interrupt received...', 'warning');
        
        // Let process manager handle active processes
        this.processManager.handleInterrupt();
        
        // Exit after a short delay
        setTimeout(() => {
            this.terminal.showMessage('Goodbye!', 'info');
            process.exit(0);
        }, 1000);
    }

    /**
     * Get system status
     */
    getSystemStatus() {
        const toolStats = this.toolManager.getStatistics();
        const activeProcesses = this.processManager.activeProcesses.size;
        const metrics = this.logger.getMetrics();

        return {
            tools: `${toolStats.available}/${toolStats.total} (${toolStats.availabilityRate}%)`,
            activeProcesses: activeProcesses,
            successRate: `${metrics.successRate}%`,
            uptime: `${Math.round(metrics.uptime / 1000)}s`
        };
    }
}

// Main execution
if (require.main === module) {
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        // Let the application handle its own cleanup
    });

    process.on('SIGTERM', () => {
        console.log('\n🛑 Received termination signal...');
        process.exit(0);
    });

    // Start Gemini-Style Black-G CLI
    try {
        new GeminiStyleBlackG();
    } catch (error) {
        console.error(`❌ Failed to start Gemini-Style Black-G CLI: ${error.message}`);
        process.exit(1);
    }
}

module.exports = GeminiStyleBlackG;
