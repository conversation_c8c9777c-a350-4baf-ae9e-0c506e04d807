# Enhanced Black-G CLI Implementation Guide

## 🚀 Interactive AI-Driven Penetration Testing System v2.1

This guide provides comprehensive instructions for implementing and using the enhanced Black-G CLI system with Gemini-like interactive features, real-time process monitoring, and enhanced permission controls.

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Architecture Components](#architecture-components)
3. [Installation & Setup](#installation--setup)
4. [Usage Examples](#usage-examples)
5. [Security Features](#security-features)
6. [Troubleshooting](#troubleshooting)

## 🏗️ System Overview

The Enhanced Black-G CLI provides:

- **Real-time Process Monitoring**: See command execution in real-time with progress indicators
- **Interactive Permission Control**: AI asks for confirmation before executing security tools
- **Risk Assessment**: Automatic risk evaluation for each command with warnings
- **Enhanced Tool Discovery**: Improved detection and installation guidance for Parrot OS
- **Gemini-like Interface**: Clean, interactive terminal experience with colors and progress bars
- **Process Control**: Ability to monitor, pause, and terminate running processes

## 🔧 Architecture Components

### Core Components

1. **InteractiveProcessManager** (`src/core/InteractiveProcessManager.js`)
   - Real-time process execution and monitoring
   - User permission control with risk assessment
   - Process status tracking and control

2. **EnhancedTerminalInterface** (`src/core/EnhancedTerminalInterface.js`)
   - Gemini-like interactive terminal experience
   - Progress bars and status indicators
   - Command history and auto-completion

3. **ParrotOSToolManager** (`src/core/ParrotOSToolManager.js`)
   - Enhanced tool discovery for Parrot OS
   - Installation guidance and PATH management
   - Tool availability reporting

4. **EnhancedLogger** (`src/utils/enhanced-logger.js`)
   - Comprehensive logging and monitoring
   - Performance metrics and alerts
   - Security event tracking

## 🛠️ Installation & Setup

### Prerequisites

Ensure you have the following installed on Parrot OS:

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js and npm
sudo apt install -y nodejs npm

# Install Go (for some security tools)
sudo apt install -y golang-go

# Install common security tools
sudo apt install -y nmap masscan sslscan dirb nikto whatweb
```

### Setup Enhanced Black-G CLI

1. **Navigate to your project directory:**
```bash
cd /home/<USER>/gemini-black
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
# Create .env file with your Gemini API key
echo "GEMINI_API_KEY=your_api_key_here" > .env
echo "BLACK_G_LOG_LEVEL=info" >> .env
```

4. **Make the enhanced CLI executable:**
```bash
chmod +x enhanced-black-g-cli.js
```

5. **Test the installation:**
```bash
node enhanced-black-g-cli.js
```

## 🎯 Usage Examples

### Basic Usage

Start the enhanced CLI:
```bash
./enhanced-black-g-cli.js
```

### Example Interactions

1. **Passive Reconnaissance:**
```
Black-G> Find subdomains for example.com
```

2. **Network Scanning:**
```
Black-G> Scan example.com for open ports and services
```

3. **Vulnerability Assessment:**
```
Black-G> Check for common vulnerabilities on https://example.com
```

4. **SSL/TLS Analysis:**
```
Black-G> Analyze SSL configuration of example.com
```

### Built-in Commands

- `help` - Show available commands and tools
- `status` - Display system status and tool availability
- `processes` - List active processes
- `history` - Show command history
- `clear` - Clear the screen
- `exit` - Exit the CLI

## 🔒 Security Features

### Permission Control System

The enhanced system includes comprehensive permission controls:

1. **Command Preview**: Shows detailed information before execution
2. **Risk Assessment**: Evaluates and displays risk level (Low/Medium/High)
3. **Target Identification**: Automatically identifies scan targets
4. **Execution Warnings**: Provides specific warnings for high-risk operations
5. **User Confirmation**: Requires explicit approval for all security tool execution

### Risk Assessment Levels

- **🟢 LOW**: Passive reconnaissance tools (subfinder, amass)
- **🟡 MEDIUM**: Active scanning tools (gobuster, nuclei)
- **🔴 HIGH**: Aggressive scanning tools (nmap -sS, masscan)

### Example Permission Dialog

```
================================================================================
🔍 COMMAND EXECUTION PREVIEW
================================================================================
📋 Command: nmap -sS -A example.com
⚠️  Risk Level: HIGH
🎯 Target: example.com
⏱️  Estimated Duration: 2-10 minutes

⚠️  WARNINGS:
   • Network scanning may trigger security alerts
   • TCP scanning detected - may be logged by target
   • Aggressive scanning enabled - higher detection risk

💡 RECOMMENDATIONS:
   • Ensure you have permission to scan the target

================================================================================

❓ Proceed with command execution? (y/N):
```

## 🔍 Process Monitoring Features

### Real-time Output Display

```
🚀 Starting: nmap -sS example.com
📊 Process ID: proc_1703123456789_1
────────────────────────────────────────────────────────────────────────────────
[14:30:15] Starting Nmap 7.80 ( https://nmap.org ) at 2024-01-01 14:30 UTC
[14:30:16] Nmap scan report for example.com (*************)
[14:30:17] Host is up (0.12s latency).
[14:30:18] PORT     STATE SERVICE
[14:30:19] 80/tcp   open  http
[14:30:20] 443/tcp  open  https
────────────────────────────────────────────────────────────────────────────────
✅ Process completed in 5s
📊 Exit Code: 0
📝 Output Lines: 15
```

### Process Control

- **Real-time monitoring**: See command output as it happens
- **Process status**: Track running, completed, and failed processes
- **Interrupt handling**: Ctrl+C to pause, Ctrl+Z to terminate
- **Process listing**: View all active processes with `processes` command

## 🛠️ Tool Management

### Automatic Tool Discovery

The system automatically discovers and reports available security tools:

```
🔧 Initializing Parrot OS Tool Manager...
🔍 System Environment Check:
   ✅ parrotOS: OK
   ✅ goInstalled: OK
   ✅ aptAvailable: OK
   ✅ internetConnection: OK

🔍 Discovering security tools...
   ✅ nmap: /usr/bin/nmap
   ✅ masscan: /usr/bin/masscan
   ❌ subfinder: Not found
   ❌ nuclei: Not found

📊 Tool Availability Report:
═══════════════════════════════════════════════════════════════

🔧 NETWORK SCANNING:
   ✅ nmap         - Network exploration tool and security scanner
   ✅ masscan      - High-speed TCP port scanner

❌ MISSING TOOLS:
   • subfinder    - Subdomain discovery tool
   • nuclei       - Vulnerability scanner based on templates

🚀 Installation Suggestions:
────────────────────────────────────────

🐹 Install via Go:
go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest
```

### PATH Configuration Check

The system checks and provides guidance for PATH configuration:

```
🛤️  PATH Configuration Check:
   ✅ /usr/bin: In PATH
   ✅ /usr/local/bin: In PATH
   ⚠️  /home/<USER>/go/bin: Missing from PATH

💡 To fix PATH issues, add this to your ~/.bashrc or ~/.zshrc:
export PATH="/home/<USER>/go/bin:$PATH"
```

## 🐛 Troubleshooting

### Common Issues

1. **ENOENT Errors (Tool Not Found)**
   - **Cause**: Security tools not installed or not in PATH
   - **Solution**: Use the tool installation suggestions provided by the system

2. **Permission Denied Errors**
   - **Cause**: Insufficient permissions for tool execution
   - **Solution**: Ensure tools have execute permissions: `chmod +x /path/to/tool`

3. **API Key Issues**
   - **Cause**: Missing or invalid Gemini API key
   - **Solution**: Set `GEMINI_API_KEY` in your `.env` file

4. **Process Hanging**
   - **Cause**: Long-running security tools
   - **Solution**: Use Ctrl+C to interrupt or check `processes` command

### Debug Mode

Enable debug logging for troubleshooting:

```bash
export BLACK_G_LOG_LEVEL=debug
./enhanced-black-g-cli.js
```

### Tool Installation Issues

If tools are not detected after installation:

1. **Refresh tool discovery:**
```
Black-G> status
```

2. **Check PATH configuration:**
```bash
echo $PATH
which nmap
which subfinder
```

3. **Restart the CLI** after installing new tools

## 📊 Performance Monitoring

The system provides comprehensive performance monitoring:

- **Success Rate**: Percentage of successful command executions
- **Response Time**: Average time for AI processing
- **Tool Performance**: Individual tool execution statistics
- **System Metrics**: Memory usage, uptime, active processes

Access performance data with:
```
Black-G> status
```

## 🔐 Security Best Practices

1. **Always verify target authorization** before running scans
2. **Use appropriate scan intensity** for the target environment
3. **Monitor scan impact** on target systems
4. **Follow responsible disclosure** for any vulnerabilities found
5. **Keep tools updated** for latest security features
6. **Review command previews** carefully before approval
7. **Use rate limiting** for production environments

## 📝 Logging and Reporting

The enhanced system provides comprehensive logging:

- **Command execution logs**: All executed commands with timestamps
- **Performance metrics**: Tool execution times and success rates
- **Security events**: Alerts and warnings
- **Error tracking**: Detailed error information for troubleshooting

Logs are stored in the `./logs` directory with automatic rotation.

## 🚀 Next Steps

After successful implementation:

1. **Test with safe targets** to verify functionality
2. **Customize tool configurations** as needed
3. **Set up monitoring dashboards** for team environments
4. **Integrate with existing security workflows**
5. **Train team members** on the enhanced interface

For additional support or feature requests, refer to the project documentation or contact the development team.
