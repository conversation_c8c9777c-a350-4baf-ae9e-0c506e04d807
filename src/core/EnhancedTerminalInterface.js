/**
 * Enhanced Terminal Interface for Black-G CLI
 * Provides Gemini-like interactive terminal experience with real-time updates
 */

const readline = require('readline');
const { EventEmitter } = require('events');

class EnhancedTerminalInterface extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            prompt: options.prompt || 'Black-G> ',
            enableColors: options.enableColors !== false,
            enableProgressBars: options.enableProgressBars !== false,
            maxHistorySize: options.maxHistorySize || 1000,
            autoComplete: options.autoComplete !== false,
            ...options
        };

        this.commandHistory = [];
        this.currentHistoryIndex = -1;
        this.activeProgressBars = new Map();
        this.statusLines = new Map();
        
        // Color definitions
        this.colors = {
            reset: '\x1b[0m',
            bright: '\x1b[1m',
            dim: '\x1b[2m',
            red: '\x1b[31m',
            green: '\x1b[32m',
            yellow: '\x1b[33m',
            blue: '\x1b[34m',
            magenta: '\x1b[35m',
            cyan: '\x1b[36m',
            white: '\x1b[37m',
            bgRed: '\x1b[41m',
            bgGreen: '\x1b[42m',
            bgYellow: '\x1b[43m',
            bgBlue: '\x1b[44m'
        };

        // Terminal control sequences
        this.terminal = {
            clearLine: '\r\x1b[K',
            clearScreen: '\x1b[2J\x1b[H',
            saveCursor: '\x1b[s',
            restoreCursor: '\x1b[u',
            hideCursor: '\x1b[?25l',
            showCursor: '\x1b[?25h',
            moveUp: (lines) => `\x1b[${lines}A`,
            moveDown: (lines) => `\x1b[${lines}B`,
            moveToColumn: (col) => `\x1b[${col}G`
        };

        this.setupInterface();
    }

    /**
     * Setup the readline interface with enhanced features
     */
    setupInterface() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: this.colorize(this.options.prompt, 'cyan'),
            completer: this.options.autoComplete ? this.completer.bind(this) : undefined,
            historySize: this.options.maxHistorySize
        });

        // Setup event handlers
        this.rl.on('line', (input) => this.handleInput(input.trim()));
        this.rl.on('close', () => this.handleClose());
        this.rl.on('SIGINT', () => this.handleInterrupt());

        // Setup custom key handlers
        this.setupKeyHandlers();
        
        // Show initial banner
        this.showBanner();
        this.rl.prompt();
    }

    /**
     * Setup custom key handlers for enhanced navigation
     */
    setupKeyHandlers() {
        process.stdin.on('keypress', (str, key) => {
            if (!key) return;

            // Handle history navigation
            if (key.name === 'up') {
                this.navigateHistory('up');
            } else if (key.name === 'down') {
                this.navigateHistory('down');
            }
            
            // Handle process control
            if (key.ctrl && key.name === 'c') {
                this.emit('interrupt');
            }
            
            if (key.ctrl && key.name === 'z') {
                this.emit('suspend');
            }
        });
    }

    /**
     * Handle user input
     */
    handleInput(input) {
        if (!input) {
            this.rl.prompt();
            return;
        }

        // Add to history
        this.addToHistory(input);
        
        // Handle built-in commands
        if (this.handleBuiltinCommands(input)) {
            return;
        }

        // Emit input event for processing
        this.emit('input', input);
    }

    /**
     * Handle built-in terminal commands
     */
    handleBuiltinCommands(input) {
        const command = input.toLowerCase();
        
        switch (command) {
            case 'clear':
                this.clearScreen();
                return true;
                
            case 'history':
                this.showHistory();
                return true;
                
            case 'status':
                this.emit('statusRequest');
                return true;
                
            case 'processes':
                this.emit('processListRequest');
                return true;
                
            case 'help':
                this.showHelp();
                return true;
                
            case 'exit':
            case 'quit':
                this.handleClose();
                return true;
                
            default:
                return false;
        }
    }

    /**
     * Auto-completion for commands and tools
     */
    completer(line) {
        const completions = [
            // Security tools
            'nmap', 'masscan', 'subfinder', 'amass', 'nuclei', 'gobuster', 'sslscan',
            // Built-in commands
            'help', 'status', 'history', 'clear', 'processes', 'exit',
            // Common options
            '-h', '--help', '-v', '--version', '-o', '--output'
        ];

        const hits = completions.filter((c) => c.startsWith(line));
        return [hits.length ? hits : completions, line];
    }

    /**
     * Navigate command history
     */
    navigateHistory(direction) {
        if (this.commandHistory.length === 0) return;

        if (direction === 'up') {
            if (this.currentHistoryIndex < this.commandHistory.length - 1) {
                this.currentHistoryIndex++;
            }
        } else if (direction === 'down') {
            if (this.currentHistoryIndex > -1) {
                this.currentHistoryIndex--;
            }
        }

        const command = this.currentHistoryIndex >= 0 
            ? this.commandHistory[this.commandHistory.length - 1 - this.currentHistoryIndex]
            : '';

        // Clear current line and show history command
        this.rl.write(null, { ctrl: true, name: 'u' });
        this.rl.write(command);
    }

    /**
     * Add command to history
     */
    addToHistory(command) {
        if (command && command !== this.commandHistory[this.commandHistory.length - 1]) {
            this.commandHistory.push(command);
            if (this.commandHistory.length > this.options.maxHistorySize) {
                this.commandHistory.shift();
            }
        }
        this.currentHistoryIndex = -1;
    }

    /**
     * Show command history
     */
    showHistory() {
        console.log('\n' + this.colorize('📜 Command History:', 'yellow'));
        console.log('─'.repeat(50));
        
        this.commandHistory.slice(-20).forEach((cmd, index) => {
            const lineNumber = this.commandHistory.length - 20 + index + 1;
            console.log(`${this.colorize(lineNumber.toString().padStart(3), 'dim')} ${cmd}`);
        });
        
        console.log('');
        this.rl.prompt();
    }

    /**
     * Show help information
     */
    showHelp() {
        console.log(`
${this.colorize('🔧 Black-G CLI - Interactive Commands', 'cyan')}
${'═'.repeat(60)}

${this.colorize('Built-in Commands:', 'yellow')}
  help                 Show this help message
  status               Show system status
  processes            List active processes
  history              Show command history
  clear                Clear the screen
  exit/quit            Exit the CLI

${this.colorize('Security Tools:', 'green')}
  nmap [options]       Network mapping and port scanning
  masscan [options]    High-speed port scanner
  subfinder [options]  Subdomain discovery
  amass [options]      Attack surface mapping
  nuclei [options]     Vulnerability scanner
  gobuster [options]   Directory/file brute-forcer
  sslscan [options]    SSL/TLS scanner

${this.colorize('Navigation:', 'blue')}
  ↑/↓ arrows          Navigate command history
  Tab                 Auto-complete commands
  Ctrl+C              Interrupt current process
  Ctrl+Z              Suspend current process

${this.colorize('Examples:', 'magenta')}
  "Scan example.com for open ports"
  "Find subdomains of target.com"
  "Check SSL vulnerabilities on my servers"
`);
        this.rl.prompt();
    }

    /**
     * Show banner
     */
    showBanner() {
        const banner = `
${this.colorize('╔══════════════════════════════════════════════════════════════════════╗', 'cyan')}
${this.colorize('║', 'cyan')}${this.colorize('                          BLACK-G CLI v2.0                           ', 'white')}${this.colorize('║', 'cyan')}
${this.colorize('║', 'cyan')}${this.colorize('                Interactive AI-Driven Penetration Testing            ', 'white')}${this.colorize('║', 'cyan')}
${this.colorize('╚══════════════════════════════════════════════════════════════════════╝', 'cyan')}

${this.colorize('🚀 Enhanced Interactive Mode Active', 'green')}
${this.colorize('💡 Type your security testing requests in natural language', 'yellow')}
${this.colorize('🔧 Type "help" for available commands', 'blue')}
`;
        console.log(banner);
    }

    /**
     * Create and display a progress bar
     */
    createProgressBar(id, label, total = 100) {
        const progressBar = {
            id,
            label,
            current: 0,
            total,
            startTime: Date.now(),
            lastUpdate: Date.now()
        };
        
        this.activeProgressBars.set(id, progressBar);
        this.updateProgressBar(id, 0);
        return id;
    }

    /**
     * Update progress bar
     */
    updateProgressBar(id, current, status = '') {
        const progressBar = this.activeProgressBars.get(id);
        if (!progressBar) return;

        progressBar.current = current;
        progressBar.lastUpdate = Date.now();
        
        const percentage = Math.min(100, Math.max(0, (current / progressBar.total) * 100));
        const barLength = 40;
        const filledLength = Math.round((percentage / 100) * barLength);
        const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
        
        const elapsed = Date.now() - progressBar.startTime;
        const rate = current / (elapsed / 1000);
        const eta = current > 0 ? ((progressBar.total - current) / rate) : 0;
        
        const progressLine = `${this.colorize(progressBar.label, 'cyan')}: [${this.colorize(bar, 'green')}] ${percentage.toFixed(1)}% ${status}`;
        
        // Save cursor, move to progress line, update, restore cursor
        process.stdout.write(this.terminal.saveCursor);
        process.stdout.write(this.terminal.clearLine);
        process.stdout.write(progressLine);
        process.stdout.write(this.terminal.restoreCursor);
    }

    /**
     * Complete and remove progress bar
     */
    completeProgressBar(id, finalMessage = 'Complete') {
        const progressBar = this.activeProgressBars.get(id);
        if (!progressBar) return;

        this.updateProgressBar(id, progressBar.total, finalMessage);
        console.log(''); // Move to next line
        this.activeProgressBars.delete(id);
    }

    /**
     * Display status line
     */
    showStatusLine(id, message, type = 'info') {
        const colorMap = {
            info: 'cyan',
            success: 'green',
            warning: 'yellow',
            error: 'red'
        };
        
        const color = colorMap[type] || 'cyan';
        const statusMessage = `${this.colorize('●', color)} ${message}`;
        
        console.log(statusMessage);
        this.statusLines.set(id, { message: statusMessage, timestamp: Date.now() });
    }

    /**
     * Clear screen
     */
    clearScreen() {
        console.log(this.terminal.clearScreen);
        this.showBanner();
        this.rl.prompt();
    }

    /**
     * Colorize text
     */
    colorize(text, color) {
        if (!this.options.enableColors) return text;
        return `${this.colors[color] || ''}${text}${this.colors.reset}`;
    }

    /**
     * Print formatted message
     */
    print(message, type = 'info') {
        const icons = {
            info: '💡',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            process: '🔄'
        };
        
        const colors = {
            info: 'cyan',
            success: 'green',
            warning: 'yellow',
            error: 'red',
            process: 'blue'
        };
        
        const icon = icons[type] || icons.info;
        const color = colors[type] || colors.info;
        
        console.log(`${icon} ${this.colorize(message, color)}`);
    }

    /**
     * Handle interrupt signal
     */
    handleInterrupt() {
        console.log('\n🛑 Interrupt received...');
        this.emit('interrupt');
    }

    /**
     * Handle interface close
     */
    handleClose() {
        console.log('\n👋 Goodbye!');
        this.rl.close();
        process.exit(0);
    }

    /**
     * Prompt for next input
     */
    prompt() {
        this.rl.prompt();
    }

    /**
     * Write output without interfering with prompt
     */
    write(text) {
        // Clear current line, write text, restore prompt
        process.stdout.write(this.terminal.clearLine);
        console.log(text);
        this.rl.prompt();
    }

    /**
     * Get interface for external use
     */
    getInterface() {
        return this.rl;
    }
}

module.exports = EnhancedTerminalInterface;
