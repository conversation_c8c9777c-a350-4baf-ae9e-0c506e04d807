/**
 * Gemini-Style Terminal Interface for Black-G CLI
 * Creates terminal interface exactly like Gemini CLI with real-time command boxes
 */

const readline = require('readline');
const { EventEmitter } = require('events');

class GeminiStyleTerminal extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            enableColors: options.enableColors !== false,
            maxConcurrentBoxes: options.maxConcurrentBoxes || 5,
            boxHeight: options.boxHeight || 8,
            boxWidth: options.boxWidth || 120,
            ...options
        };

        this.activeBoxes = new Map();
        this.completedBoxes = [];
        this.currentBoxId = 0;
        this.terminalHeight = process.stdout.rows || 24;
        this.terminalWidth = process.stdout.columns || 120;
        
        // Terminal control sequences
        this.term = {
            clear: '\x1b[2J\x1b[H',
            clearLine: '\r\x1b[K',
            saveCursor: '\x1b[s',
            restoreCursor: '\x1b[u',
            hideCursor: '\x1b[?25l',
            showCursor: '\x1b[?25h',
            moveUp: (lines) => `\x1b[${lines}A`,
            moveDown: (lines) => `\x1b[${lines}B`,
            moveTo: (row, col) => `\x1b[${row};${col}H`,
            scrollUp: '\x1b[S',
            scrollDown: '\x1b[T'
        };

        // Colors matching Gemini CLI
        this.colors = {
            reset: '\x1b[0m',
            bright: '\x1b[1m',
            dim: '\x1b[2m',
            
            // Text colors
            black: '\x1b[30m',
            red: '\x1b[31m',
            green: '\x1b[32m',
            yellow: '\x1b[33m',
            blue: '\x1b[34m',
            magenta: '\x1b[35m',
            cyan: '\x1b[36m',
            white: '\x1b[37m',
            
            // Bright colors
            brightRed: '\x1b[91m',
            brightGreen: '\x1b[92m',
            brightYellow: '\x1b[93m',
            brightBlue: '\x1b[94m',
            brightMagenta: '\x1b[95m',
            brightCyan: '\x1b[96m',
            brightWhite: '\x1b[97m',
            
            // Background colors
            bgBlack: '\x1b[40m',
            bgRed: '\x1b[41m',
            bgGreen: '\x1b[42m',
            bgYellow: '\x1b[43m',
            bgBlue: '\x1b[44m',
            bgMagenta: '\x1b[45m',
            bgCyan: '\x1b[46m',
            bgWhite: '\x1b[47m'
        };

        this.setupInterface();
    }

    /**
     * Setup the terminal interface
     */
    setupInterface() {
        // Hide cursor for cleaner display
        process.stdout.write(this.term.hideCursor);
        
        // Setup readline for input
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: ''
        });

        // Handle terminal resize
        process.stdout.on('resize', () => {
            this.terminalHeight = process.stdout.rows || 24;
            this.terminalWidth = process.stdout.columns || 120;
            this.redrawInterface();
        });

        // Setup cleanup on exit
        process.on('SIGINT', () => this.cleanup());
        process.on('SIGTERM', () => this.cleanup());
        process.on('exit', () => this.cleanup());

        this.showHeader();
        this.showPrompt();
    }

    /**
     * Show header like Gemini CLI
     */
    showHeader() {
        console.log(this.term.clear);
        console.log(`${this.colors.brightCyan}╔══════════════════════════════════════════════════════════════════════╗${this.colors.reset}`);
        console.log(`${this.colors.brightCyan}║${this.colors.brightWhite}                          BLACK-G CLI v2.0                           ${this.colors.brightCyan}║${this.colors.reset}`);
        console.log(`${this.colors.brightCyan}║${this.colors.brightWhite}                Interactive AI-Driven Penetration Testing            ${this.colors.brightCyan}║${this.colors.reset}`);
        console.log(`${this.colors.brightCyan}╚══════════════════════════════════════════════════════════════════════╝${this.colors.reset}`);
        console.log('');
    }

    /**
     * Create a new command execution box like Gemini CLI
     */
    createCommandBox(command, description = '') {
        const boxId = ++this.currentBoxId;
        const box = {
            id: boxId,
            command,
            description,
            status: 'starting',
            progress: 0,
            output: [],
            startTime: Date.now(),
            currentLine: 0,
            maxLines: this.options.boxHeight - 4 // Account for borders and header
        };

        this.activeBoxes.set(boxId, box);
        this.drawCommandBox(box);
        
        return boxId;
    }

    /**
     * Draw a command execution box
     */
    drawCommandBox(box) {
        const width = Math.min(this.options.boxWidth, this.terminalWidth - 4);
        const statusColor = this.getStatusColor(box.status);
        const progressBar = this.createProgressBar(box.progress, width - 20);
        
        // Box header
        console.log(`${this.colors.white}┌${'─'.repeat(width - 2)}┐${this.colors.reset}`);
        
        // Command line with status
        const commandLine = this.truncateText(`Shell ${box.command}`, width - 10);
        console.log(`${this.colors.white}│ ${statusColor}▶${this.colors.reset} ${commandLine}${' '.repeat(Math.max(0, width - commandLine.length - 5))}${this.colors.white}│${this.colors.reset}`);
        
        // Progress line
        if (box.status === 'running') {
            console.log(`${this.colors.white}│ ${this.colors.green}Progress:${this.colors.reset} ${progressBar} ${this.colors.white}│${this.colors.reset}`);
        } else if (box.status === 'completed') {
            console.log(`${this.colors.white}│ ${this.colors.brightGreen}Command exited with code: 0${this.colors.reset}${' '.repeat(Math.max(0, width - 25))}${this.colors.white}│${this.colors.reset}`);
        } else if (box.status === 'failed') {
            console.log(`${this.colors.white}│ ${this.colors.brightRed}Command exited with code: 1${this.colors.reset}${' '.repeat(Math.max(0, width - 25))}${this.colors.white}│${this.colors.reset}`);
        }
        
        // Separator
        console.log(`${this.colors.white}├${'─'.repeat(width - 2)}┤${this.colors.reset}`);
        
        // Output area
        const outputLines = box.output.slice(-box.maxLines);
        for (let i = 0; i < box.maxLines; i++) {
            const line = outputLines[i] || '';
            const truncatedLine = this.truncateText(line, width - 4);
            const padding = ' '.repeat(Math.max(0, width - truncatedLine.length - 4));
            console.log(`${this.colors.white}│ ${this.colors.reset}${truncatedLine}${padding}${this.colors.white}│${this.colors.reset}`);
        }
        
        // Box footer
        console.log(`${this.colors.white}└${'─'.repeat(width - 2)}┘${this.colors.reset}`);
        console.log('');
    }

    /**
     * Update command box with new output
     */
    updateCommandBox(boxId, newOutput, status = null, progress = null) {
        const box = this.activeBoxes.get(boxId);
        if (!box) return;

        if (newOutput) {
            // Split output into lines and add to box
            const lines = newOutput.split('\n').filter(line => line.trim());
            box.output.push(...lines);
            
            // Keep only recent lines to prevent memory issues
            if (box.output.length > 100) {
                box.output = box.output.slice(-50);
            }
        }

        if (status) {
            box.status = status;
        }

        if (progress !== null) {
            box.progress = Math.min(100, Math.max(0, progress));
        }

        // Redraw the interface
        this.redrawInterface();
    }

    /**
     * Complete a command box
     */
    completeCommandBox(boxId, exitCode = 0) {
        const box = this.activeBoxes.get(boxId);
        if (!box) return;

        box.status = exitCode === 0 ? 'completed' : 'failed';
        box.progress = 100;
        box.endTime = Date.now();
        box.duration = box.endTime - box.startTime;

        // Move to completed boxes
        this.completedBoxes.push(box);
        this.activeBoxes.delete(boxId);

        this.redrawInterface();
    }

    /**
     * Create progress bar like Gemini CLI
     */
    createProgressBar(progress, width = 40) {
        const filled = Math.round((progress / 100) * width);
        const empty = width - filled;
        const bar = `${this.colors.brightGreen}${'█'.repeat(filled)}${this.colors.dim}${'░'.repeat(empty)}${this.colors.reset}`;
        return `${bar} ${this.colors.brightCyan}${progress.toFixed(1)}%${this.colors.reset}`;
    }

    /**
     * Get status color
     */
    getStatusColor(status) {
        switch (status) {
            case 'starting': return this.colors.brightYellow;
            case 'running': return this.colors.brightGreen;
            case 'completed': return this.colors.brightGreen;
            case 'failed': return this.colors.brightRed;
            case 'cancelled': return this.colors.brightMagenta;
            default: return this.colors.white;
        }
    }

    /**
     * Truncate text to fit width
     */
    truncateText(text, maxWidth) {
        if (text.length <= maxWidth) return text;
        return text.substring(0, maxWidth - 3) + '...';
    }

    /**
     * Redraw the entire interface
     */
    redrawInterface() {
        // Clear screen and show header
        this.showHeader();
        
        // Draw all active boxes
        for (const box of this.activeBoxes.values()) {
            this.drawCommandBox(box);
        }
        
        // Draw recent completed boxes (last 2)
        const recentCompleted = this.completedBoxes.slice(-2);
        for (const box of recentCompleted) {
            this.drawCommandBox(box);
        }
        
        this.showPrompt();
    }

    /**
     * Show command prompt at bottom
     */
    showPrompt() {
        const promptText = `${this.colors.brightCyan}~/gemini-cli${this.colors.reset} ${this.colors.brightWhite}(main*)${this.colors.reset}`;
        const statusText = `${this.colors.dim}no sandbox${this.colors.reset} ${this.colors.dim}(see /docs)${this.colors.reset}`;
        const versionText = `${this.colors.dim}gemini-2.5-flash (98% context left)${this.colors.reset}`;
        
        // Calculate spacing
        const leftSide = promptText;
        const rightSide = versionText;
        const spacing = ' '.repeat(Math.max(0, this.terminalWidth - this.stripAnsi(leftSide).length - this.stripAnsi(rightSide).length));
        
        console.log(`${leftSide}${spacing}${rightSide}`);
        console.log('');
        
        // Input line
        process.stdout.write(`${this.colors.brightCyan}Black-G>${this.colors.reset} `);
    }

    /**
     * Strip ANSI codes for length calculation
     */
    stripAnsi(str) {
        return str.replace(/\x1b\[[0-9;]*m/g, '');
    }

    /**
     * Handle user input
     */
    handleInput(input) {
        if (!input.trim()) return;
        
        // Clear the input line
        process.stdout.write('\r\x1b[K');
        
        // Emit input event
        this.emit('input', input.trim());
    }

    /**
     * Setup input handling
     */
    setupInput() {
        this.rl.on('line', (input) => {
            this.handleInput(input);
            this.redrawInterface();
        });

        // Handle special keys
        process.stdin.on('keypress', (str, key) => {
            if (key && key.ctrl && key.name === 'c') {
                this.emit('interrupt');
            }
        });
    }

    /**
     * Show a message in the interface
     */
    showMessage(message, type = 'info') {
        const colors = {
            info: this.colors.brightCyan,
            success: this.colors.brightGreen,
            warning: this.colors.brightYellow,
            error: this.colors.brightRed
        };
        
        const color = colors[type] || colors.info;
        console.log(`${color}● ${message}${this.colors.reset}`);
    }

    /**
     * Simulate command execution with real-time updates
     */
    simulateCommand(command, duration = 5000) {
        const boxId = this.createCommandBox(command);
        const box = this.activeBoxes.get(boxId);

        // Simulate progress and output
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 10;

            // Add some sample output
            const outputs = [
                `[INFO] Starting ${command}...`,
                `[INFO] Connecting to target...`,
                `[INFO] Scanning in progress...`,
                `[INFO] Found potential vulnerabilities...`,
                `[INFO] Analyzing results...`,
                `[INFO] Generating report...`
            ];

            if (Math.random() > 0.7) {
                const randomOutput = outputs[Math.floor(Math.random() * outputs.length)];
                this.updateCommandBox(boxId, randomOutput, 'running', progress);
            } else {
                this.updateCommandBox(boxId, null, 'running', progress);
            }

            if (progress >= 100) {
                clearInterval(interval);
                this.updateCommandBox(boxId, '[INFO] Command completed successfully', 'completed', 100);
                setTimeout(() => {
                    this.completeCommandBox(boxId, 0);
                }, 1000);
            }
        }, 200);

        return boxId;
    }

    /**
     * Start the interface
     */
    start() {
        this.setupInput();
        this.redrawInterface();

        // Show cursor for input
        process.stdout.write(this.term.showCursor);
    }

    /**
     * Cleanup on exit
     */
    cleanup() {
        process.stdout.write(this.term.showCursor);
        process.stdout.write('\n');
        if (this.rl) {
            this.rl.close();
        }
    }
}

module.exports = GeminiStyleTerminal;
