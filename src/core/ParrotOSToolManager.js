/**
 * Parrot OS Tool Manager for Black-G CLI
 * Enhanced tool discovery, installation guidance, and PATH management
 * Specifically designed for Parrot OS Security Edition
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { EventEmitter } = require('events');

class ParrotOSToolManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            enableAutoInstall: options.enableAutoInstall || false,
            enablePathFix: options.enablePathFix !== false,
            cacheTimeout: options.cacheTimeout || 300000, // 5 minutes
            ...options
        };

        // Parrot OS specific paths
        this.parrotPaths = [
            '/usr/bin',
            '/usr/local/bin',
            '/usr/sbin',
            '/usr/local/sbin',
            '/opt',
            '/snap/bin',
            '/home/<USER>/go/bin',
            '/home/<USER>/.cargo/bin',
            '/home/<USER>/.local/bin',
            '/usr/share/nmap/scripts',
            '/usr/share/wordlists',
            '/usr/share/seclists'
        ];

        // Tool definitions with Parrot OS specific information
        this.toolDefinitions = {
            nmap: {
                package: 'nmap',
                paths: ['/usr/bin/nmap'],
                installCommand: 'sudo apt update && sudo apt install -y nmap',
                description: 'Network exploration tool and security scanner',
                category: 'network_scanning',
                riskLevel: 'high'
            },
            masscan: {
                package: 'masscan',
                paths: ['/usr/bin/masscan'],
                installCommand: 'sudo apt update && sudo apt install -y masscan',
                description: 'High-speed TCP port scanner',
                category: 'network_scanning',
                riskLevel: 'high'
            },
            subfinder: {
                package: 'subfinder',
                paths: ['/usr/bin/subfinder', '/home/<USER>/go/bin/subfinder'],
                installCommand: 'go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest',
                description: 'Subdomain discovery tool',
                category: 'reconnaissance',
                riskLevel: 'low'
            },
            amass: {
                package: 'amass',
                paths: ['/usr/bin/amass', '/home/<USER>/go/bin/amass'],
                installCommand: 'go install -v github.com/OWASP/Amass/v3/...@master',
                description: 'Attack surface mapping tool',
                category: 'reconnaissance',
                riskLevel: 'low'
            },
            nuclei: {
                package: 'nuclei',
                paths: ['/usr/bin/nuclei', '/home/<USER>/go/bin/nuclei'],
                installCommand: 'go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest',
                description: 'Vulnerability scanner based on templates',
                category: 'vulnerability_scanning',
                riskLevel: 'medium'
            },
            gobuster: {
                package: 'gobuster',
                paths: ['/usr/bin/gobuster', '/home/<USER>/go/bin/gobuster'],
                installCommand: 'go install github.com/OJ/gobuster/v3@latest',
                description: 'Directory/file & DNS busting tool',
                category: 'web_scanning',
                riskLevel: 'medium'
            },
            sslscan: {
                package: 'sslscan',
                paths: ['/usr/bin/sslscan'],
                installCommand: 'sudo apt update && sudo apt install -y sslscan',
                description: 'SSL/TLS scanner',
                category: 'ssl_scanning',
                riskLevel: 'low'
            },
            dirb: {
                package: 'dirb',
                paths: ['/usr/bin/dirb'],
                installCommand: 'sudo apt update && sudo apt install -y dirb',
                description: 'Web content scanner',
                category: 'web_scanning',
                riskLevel: 'medium'
            },
            nikto: {
                package: 'nikto',
                paths: ['/usr/bin/nikto'],
                installCommand: 'sudo apt update && sudo apt install -y nikto',
                description: 'Web server scanner',
                category: 'web_scanning',
                riskLevel: 'medium'
            },
            whatweb: {
                package: 'whatweb',
                paths: ['/usr/bin/whatweb'],
                installCommand: 'sudo apt update && sudo apt install -y whatweb',
                description: 'Web technology identifier',
                category: 'web_scanning',
                riskLevel: 'low'
            }
        };

        this.toolCache = new Map();
        this.lastScan = 0;
        this.availableTools = new Map();
        this.missingTools = new Set();
        
        this.initialize();
    }

    /**
     * Initialize the tool manager
     */
    async initialize() {
        console.log('🔧 Initializing Parrot OS Tool Manager...');
        
        // Check system environment
        await this.checkSystemEnvironment();
        
        // Discover available tools
        await this.discoverAllTools();
        
        // Check PATH configuration
        this.checkPathConfiguration();
        
        // Generate tool report
        this.generateToolReport();
        
        this.emit('initialized', {
            availableTools: this.availableTools.size,
            missingTools: this.missingTools.size
        });
    }

    /**
     * Check system environment and prerequisites
     */
    async checkSystemEnvironment() {
        const checks = {
            parrotOS: await this.isParrotOS(),
            goInstalled: await this.isGoInstalled(),
            aptAvailable: await this.isAptAvailable(),
            internetConnection: await this.hasInternetConnection()
        };

        console.log('🔍 System Environment Check:');
        Object.entries(checks).forEach(([check, result]) => {
            const status = result ? '✅' : '❌';
            console.log(`   ${status} ${check}: ${result ? 'OK' : 'MISSING'}`);
        });

        return checks;
    }

    /**
     * Check if running on Parrot OS
     */
    async isParrotOS() {
        try {
            const osRelease = fs.readFileSync('/etc/os-release', 'utf8');
            return osRelease.includes('Parrot') || osRelease.includes('parrot');
        } catch {
            return false;
        }
    }

    /**
     * Check if Go is installed
     */
    async isGoInstalled() {
        return new Promise((resolve) => {
            const child = spawn('go', ['version'], { stdio: 'pipe' });
            child.on('close', (code) => resolve(code === 0));
            child.on('error', () => resolve(false));
        });
    }

    /**
     * Check if apt is available
     */
    async isAptAvailable() {
        return new Promise((resolve) => {
            const child = spawn('apt', ['--version'], { stdio: 'pipe' });
            child.on('close', (code) => resolve(code === 0));
            child.on('error', () => resolve(false));
        });
    }

    /**
     * Check internet connection
     */
    async hasInternetConnection() {
        return new Promise((resolve) => {
            const child = spawn('ping', ['-c', '1', '*******'], { stdio: 'pipe' });
            child.on('close', (code) => resolve(code === 0));
            child.on('error', () => resolve(false));
        });
    }

    /**
     * Discover all security tools
     */
    async discoverAllTools() {
        console.log('🔍 Discovering security tools...');
        
        for (const [toolName, toolDef] of Object.entries(this.toolDefinitions)) {
            const discovered = await this.discoverTool(toolName, toolDef);
            
            if (discovered) {
                this.availableTools.set(toolName, discovered);
                console.log(`   ✅ ${toolName}: ${discovered.path}`);
            } else {
                this.missingTools.add(toolName);
                console.log(`   ❌ ${toolName}: Not found`);
            }
        }
    }

    /**
     * Discover a specific tool
     */
    async discoverTool(toolName, toolDef) {
        // Check cache first
        if (this.toolCache.has(toolName)) {
            const cached = this.toolCache.get(toolName);
            if (Date.now() - cached.timestamp < this.options.cacheTimeout) {
                return cached.data;
            }
        }

        let discovered = null;

        // Try 'which' command first
        const whichResult = await this.executeWhich(toolName);
        if (whichResult) {
            discovered = {
                name: toolName,
                path: whichResult.trim(),
                method: 'which',
                ...toolDef
            };
        } else {
            // Try predefined paths
            for (const testPath of toolDef.paths) {
                if (fs.existsSync(testPath)) {
                    try {
                        fs.accessSync(testPath, fs.constants.X_OK);
                        discovered = {
                            name: toolName,
                            path: testPath,
                            method: 'path_search',
                            ...toolDef
                        };
                        break;
                    } catch {
                        // Not executable
                    }
                }
            }
        }

        // Cache the result
        this.toolCache.set(toolName, {
            data: discovered,
            timestamp: Date.now()
        });

        return discovered;
    }

    /**
     * Execute 'which' command
     */
    async executeWhich(toolName) {
        return new Promise((resolve) => {
            const child = spawn('which', [toolName], { stdio: 'pipe' });
            let output = '';
            
            child.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            child.on('close', (code) => {
                resolve(code === 0 ? output.trim() : null);
            });
            
            child.on('error', () => resolve(null));
        });
    }

    /**
     * Check PATH configuration
     */
    checkPathConfiguration() {
        const currentPath = process.env.PATH || '';
        const pathDirs = currentPath.split(':');
        const missingPaths = [];

        console.log('\n🛤️  PATH Configuration Check:');
        
        for (const parrotPath of this.parrotPaths) {
            if (fs.existsSync(parrotPath)) {
                if (pathDirs.includes(parrotPath)) {
                    console.log(`   ✅ ${parrotPath}: In PATH`);
                } else {
                    console.log(`   ⚠️  ${parrotPath}: Missing from PATH`);
                    missingPaths.push(parrotPath);
                }
            }
        }

        if (missingPaths.length > 0) {
            console.log('\n💡 To fix PATH issues, add this to your ~/.bashrc or ~/.zshrc:');
            console.log(`export PATH="${missingPaths.join(':')}:$PATH"`);
        }

        return missingPaths;
    }

    /**
     * Generate comprehensive tool report
     */
    generateToolReport() {
        console.log('\n📊 Tool Availability Report:');
        console.log('═'.repeat(60));
        
        const categories = {};
        
        // Group tools by category
        for (const [toolName, toolInfo] of this.availableTools) {
            const category = toolInfo.category || 'other';
            if (!categories[category]) categories[category] = [];
            categories[category].push({ name: toolName, ...toolInfo });
        }

        // Display available tools by category
        for (const [category, tools] of Object.entries(categories)) {
            console.log(`\n🔧 ${category.replace('_', ' ').toUpperCase()}:`);
            tools.forEach(tool => {
                console.log(`   ✅ ${tool.name.padEnd(12)} - ${tool.description}`);
            });
        }

        // Display missing tools
        if (this.missingTools.size > 0) {
            console.log('\n❌ MISSING TOOLS:');
            for (const toolName of this.missingTools) {
                const toolDef = this.toolDefinitions[toolName];
                console.log(`   • ${toolName.padEnd(12)} - ${toolDef.description}`);
            }
        }

        // Installation suggestions
        this.generateInstallationSuggestions();
    }

    /**
     * Generate installation suggestions
     */
    generateInstallationSuggestions() {
        if (this.missingTools.size === 0) {
            console.log('\n🎉 All security tools are available!');
            return;
        }

        console.log('\n🚀 Installation Suggestions:');
        console.log('─'.repeat(40));

        const aptTools = [];
        const goTools = [];

        for (const toolName of this.missingTools) {
            const toolDef = this.toolDefinitions[toolName];
            if (toolDef.installCommand.includes('apt')) {
                aptTools.push(toolName);
            } else if (toolDef.installCommand.includes('go install')) {
                goTools.push(toolName);
            }
        }

        if (aptTools.length > 0) {
            console.log('\n📦 Install via APT:');
            console.log(`sudo apt update && sudo apt install -y ${aptTools.join(' ')}`);
        }

        if (goTools.length > 0) {
            console.log('\n🐹 Install via Go:');
            goTools.forEach(tool => {
                const toolDef = this.toolDefinitions[tool];
                console.log(`${toolDef.installCommand}`);
            });
        }

        console.log('\n💡 After installation, restart Black-G CLI to refresh tool detection.');
    }

    /**
     * Install a specific tool
     */
    async installTool(toolName) {
        const toolDef = this.toolDefinitions[toolName];
        if (!toolDef) {
            throw new Error(`Unknown tool: ${toolName}`);
        }

        console.log(`🔧 Installing ${toolName}...`);
        console.log(`📋 Command: ${toolDef.installCommand}`);

        if (!this.options.enableAutoInstall) {
            console.log('⚠️  Auto-installation is disabled. Please run the command manually.');
            return false;
        }

        // Implementation would go here for auto-installation
        // For security reasons, this should require explicit user confirmation
        
        return true;
    }

    /**
     * Get tool information
     */
    getToolInfo(toolName) {
        return this.availableTools.get(toolName) || null;
    }

    /**
     * Check if tool is available
     */
    isToolAvailable(toolName) {
        return this.availableTools.has(toolName);
    }

    /**
     * Get tool path
     */
    getToolPath(toolName) {
        const toolInfo = this.availableTools.get(toolName);
        return toolInfo ? toolInfo.path : toolName; // Fallback to tool name
    }

    /**
     * Get all available tools
     */
    getAvailableTools() {
        return Array.from(this.availableTools.keys());
    }

    /**
     * Get missing tools
     */
    getMissingTools() {
        return Array.from(this.missingTools);
    }

    /**
     * Refresh tool discovery
     */
    async refresh() {
        console.log('🔄 Refreshing tool discovery...');
        this.toolCache.clear();
        this.availableTools.clear();
        this.missingTools.clear();
        
        await this.discoverAllTools();
        this.generateToolReport();
        
        this.emit('refreshed', {
            availableTools: this.availableTools.size,
            missingTools: this.missingTools.size
        });
    }

    /**
     * Get tool statistics
     */
    getStatistics() {
        const totalTools = Object.keys(this.toolDefinitions).length;
        const availableCount = this.availableTools.size;
        const missingCount = this.missingTools.size;
        
        return {
            total: totalTools,
            available: availableCount,
            missing: missingCount,
            availabilityRate: Math.round((availableCount / totalTools) * 100)
        };
    }
}

module.exports = ParrotOSToolManager;
