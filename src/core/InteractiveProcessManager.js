/**
 * Interactive Process Manager for Black-G CLI
 * Provides real-time process monitoring, control, and visibility
 * Similar to Gemini CLI interface with enhanced security features
 */

const { spawn } = require('child_process');
const { EventEmitter } = require('events');
const readline = require('readline');

class InteractiveProcessManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            enableRealTimeOutput: options.enableRealTimeOutput !== false,
            enableProcessControl: options.enableProcessControl !== false,
            maxConcurrentProcesses: options.maxConcurrentProcesses || 5,
            outputBufferSize: options.outputBufferSize || 1024 * 1024, // 1MB
            ...options
        };

        this.activeProcesses = new Map();
        this.processHistory = [];
        this.processCounter = 0;
        
        // Terminal control sequences
        this.terminalCodes = {
            clearLine: '\r\x1b[K',
            saveCursor: '\x1b[s',
            restoreCursor: '\x1b[u',
            hideCursor: '\x1b[?25l',
            showCursor: '\x1b[?25h',
            moveUp: (lines) => `\x1b[${lines}A`,
            moveDown: (lines) => `\x1b[${lines}B`
        };

        this.setupSignalHandlers();
    }

    /**
     * Execute command with interactive monitoring and user control
     */
    async executeInteractiveCommand(command, options = {}) {
        const processId = this.generateProcessId();
        const processInfo = {
            id: processId,
            command,
            startTime: Date.now(),
            status: 'starting',
            output: '',
            errorOutput: '',
            exitCode: null,
            pid: null,
            options: {
                requireConfirmation: options.requireConfirmation !== false,
                showRealTimeOutput: options.showRealTimeOutput !== false,
                allowUserControl: options.allowUserControl !== false,
                riskLevel: options.riskLevel || 'medium',
                ...options
            }
        };

        // Add to active processes
        this.activeProcesses.set(processId, processInfo);
        this.emit('processStarted', processInfo);

        try {
            // Show command preview and get user confirmation
            if (processInfo.options.requireConfirmation) {
                const confirmed = await this.showCommandPreview(command, processInfo.options);
                if (!confirmed) {
                    processInfo.status = 'cancelled';
                    this.activeProcesses.delete(processId);
                    this.emit('processCancelled', processInfo);
                    return { cancelled: true, processId };
                }
            }

            // Execute the command
            const result = await this.executeWithMonitoring(processInfo);
            
            // Move to history
            this.processHistory.push({
                ...processInfo,
                endTime: Date.now(),
                duration: Date.now() - processInfo.startTime
            });
            
            this.activeProcesses.delete(processId);
            this.emit('processCompleted', processInfo);
            
            return result;

        } catch (error) {
            processInfo.status = 'failed';
            processInfo.error = error.message;
            this.activeProcesses.delete(processId);
            this.emit('processFailed', processInfo);
            throw error;
        }
    }

    /**
     * Show command preview with risk assessment and get user confirmation
     */
    async showCommandPreview(command, options) {
        const riskAssessment = this.assessCommandRisk(command);
        
        console.log('\n' + '='.repeat(80));
        console.log('🔍 COMMAND EXECUTION PREVIEW');
        console.log('='.repeat(80));
        console.log(`📋 Command: ${command}`);
        console.log(`⚠️  Risk Level: ${this.formatRiskLevel(riskAssessment.level)}`);
        console.log(`🎯 Target: ${riskAssessment.target || 'Not specified'}`);
        console.log(`⏱️  Estimated Duration: ${riskAssessment.estimatedDuration}`);
        
        if (riskAssessment.warnings.length > 0) {
            console.log('\n⚠️  WARNINGS:');
            riskAssessment.warnings.forEach(warning => {
                console.log(`   • ${warning}`);
            });
        }
        
        if (riskAssessment.recommendations.length > 0) {
            console.log('\n💡 RECOMMENDATIONS:');
            riskAssessment.recommendations.forEach(rec => {
                console.log(`   • ${rec}`);
            });
        }
        
        console.log('\n' + '='.repeat(80));
        
        return await this.askUserConfirmation('Proceed with command execution?');
    }

    /**
     * Assess command risk level and provide warnings
     */
    assessCommandRisk(command) {
        const cmd = command.toLowerCase();
        const warnings = [];
        const recommendations = [];
        let level = 'low';
        let estimatedDuration = '< 1 minute';
        let target = null;

        // Extract target from common patterns
        const targetMatch = command.match(/(?:https?:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        if (targetMatch) target = targetMatch[1];

        // Risk assessment based on tool and parameters
        if (cmd.includes('nmap')) {
            level = 'high';
            estimatedDuration = '2-10 minutes';
            warnings.push('Network scanning may trigger security alerts');
            recommendations.push('Ensure you have permission to scan the target');
            
            if (cmd.includes('-sS') || cmd.includes('-sT')) {
                warnings.push('TCP scanning detected - may be logged by target');
            }
            if (cmd.includes('-A') || cmd.includes('-O')) {
                warnings.push('Aggressive scanning enabled - higher detection risk');
            }
        }

        if (cmd.includes('masscan')) {
            level = 'high';
            estimatedDuration = '1-5 minutes';
            warnings.push('High-speed scanning may overwhelm target systems');
            recommendations.push('Consider rate limiting for production targets');
        }

        if (cmd.includes('nuclei')) {
            level = 'medium';
            estimatedDuration = '5-30 minutes';
            warnings.push('Vulnerability scanning may trigger security systems');
            recommendations.push('Use appropriate templates for your scope');
        }

        if (cmd.includes('gobuster') || cmd.includes('dirb')) {
            level = 'medium';
            estimatedDuration = '2-15 minutes';
            warnings.push('Directory brute-forcing may generate significant traffic');
        }

        if (cmd.includes('subfinder') || cmd.includes('amass')) {
            level = 'low';
            estimatedDuration = '1-3 minutes';
            recommendations.push('Passive reconnaissance - generally safe');
        }

        return {
            level,
            estimatedDuration,
            target,
            warnings,
            recommendations
        };
    }

    /**
     * Format risk level with colors
     */
    formatRiskLevel(level) {
        const colors = {
            low: '\x1b[32m',    // Green
            medium: '\x1b[33m', // Yellow
            high: '\x1b[31m',   // Red
            reset: '\x1b[0m'
        };
        
        return `${colors[level] || colors.medium}${level.toUpperCase()}${colors.reset}`;
    }

    /**
     * Execute command with real-time monitoring and user control
     */
    async executeWithMonitoring(processInfo) {
        return new Promise((resolve, reject) => {
            const parts = processInfo.command.split(' ');
            const tool = parts[0];
            const args = parts.slice(1);

            console.log(`\n🚀 Starting: ${processInfo.command}`);
            console.log(`📊 Process ID: ${processInfo.id}`);
            console.log('─'.repeat(80));

            const child = spawn(tool, args, {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: process.env,
                shell: false
            });

            processInfo.pid = child.pid;
            processInfo.status = 'running';
            processInfo.child = child;

            let outputLines = 0;
            const startTime = Date.now();

            // Real-time output handling
            child.stdout.on('data', (data) => {
                const chunk = data.toString();
                processInfo.output += chunk;
                
                if (processInfo.options.showRealTimeOutput) {
                    this.displayRealTimeOutput(chunk, processInfo);
                    outputLines += chunk.split('\n').length - 1;
                }
            });

            child.stderr.on('data', (data) => {
                const chunk = data.toString();
                processInfo.errorOutput += chunk;
                
                if (processInfo.options.showRealTimeOutput) {
                    this.displayRealTimeError(chunk, processInfo);
                }
            });

            // Process completion
            child.on('close', (code) => {
                const duration = Date.now() - startTime;
                processInfo.exitCode = code;
                processInfo.status = code === 0 ? 'completed' : 'failed';
                
                console.log('\n' + '─'.repeat(80));
                console.log(`✅ Process completed in ${this.formatDuration(duration)}`);
                console.log(`📊 Exit Code: ${code}`);
                console.log(`📝 Output Lines: ${processInfo.output.split('\n').length}`);
                
                if (code === 0) {
                    resolve({
                        success: true,
                        processId: processInfo.id,
                        output: processInfo.output,
                        errorOutput: processInfo.errorOutput,
                        duration,
                        exitCode: code
                    });
                } else {
                    reject(new Error(`Process failed with exit code ${code}: ${processInfo.errorOutput}`));
                }
            });

            child.on('error', (err) => {
                processInfo.status = 'failed';
                processInfo.error = err.message;
                reject(new Error(`Failed to execute command: ${err.message}`));
            });

            // Setup user control if enabled
            if (processInfo.options.allowUserControl) {
                this.setupProcessControl(processInfo);
            }
        });
    }

    /**
     * Display real-time output with formatting
     */
    displayRealTimeOutput(chunk, processInfo) {
        const lines = chunk.split('\n');
        lines.forEach((line, index) => {
            if (line.trim()) {
                const timestamp = new Date().toLocaleTimeString();
                console.log(`\x1b[36m[${timestamp}]\x1b[0m ${line}`);
            }
        });
    }

    /**
     * Display real-time error output
     */
    displayRealTimeError(chunk, processInfo) {
        const lines = chunk.split('\n');
        lines.forEach((line, index) => {
            if (line.trim()) {
                const timestamp = new Date().toLocaleTimeString();
                console.log(`\x1b[31m[${timestamp}] ERROR:\x1b[0m ${line}`);
            }
        });
    }

    /**
     * Setup interactive process control
     */
    setupProcessControl(processInfo) {
        console.log('\n💡 Process Control: Press Ctrl+C to pause, Ctrl+Z to terminate');
        
        // Note: In a full implementation, you'd set up key listeners here
        // This is a simplified version showing the concept
    }

    /**
     * Get user confirmation
     */
    async askUserConfirmation(question) {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            rl.question(`\n❓ ${question} (y/N): `, (answer) => {
                rl.close();
                resolve(answer.toLowerCase().startsWith('y'));
            });
        });
    }

    /**
     * Generate unique process ID
     */
    generateProcessId() {
        return `proc_${Date.now()}_${++this.processCounter}`;
    }

    /**
     * Format duration in human-readable format
     */
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * Get status of all active processes
     */
    getActiveProcesses() {
        return Array.from(this.activeProcesses.values()).map(proc => ({
            id: proc.id,
            command: proc.command,
            status: proc.status,
            startTime: proc.startTime,
            duration: Date.now() - proc.startTime,
            pid: proc.pid
        }));
    }

    /**
     * Kill a specific process
     */
    killProcess(processId) {
        const processInfo = this.activeProcesses.get(processId);
        if (processInfo && processInfo.child) {
            processInfo.child.kill('SIGTERM');
            processInfo.status = 'terminated';
            console.log(`🛑 Process ${processId} terminated`);
            return true;
        }
        return false;
    }

    /**
     * Setup signal handlers for graceful shutdown
     */
    setupSignalHandlers() {
        process.on('SIGINT', () => {
            console.log('\n🛑 Received interrupt signal...');
            this.killAllProcesses();
        });

        process.on('SIGTERM', () => {
            console.log('\n🛑 Received termination signal...');
            this.killAllProcesses();
        });
    }

    /**
     * Kill all active processes
     */
    killAllProcesses() {
        console.log('🔄 Terminating all active processes...');
        for (const [processId, processInfo] of this.activeProcesses) {
            if (processInfo.child) {
                processInfo.child.kill('SIGTERM');
                console.log(`   ✓ Terminated process ${processId}`);
            }
        }
        this.activeProcesses.clear();
    }
}

module.exports = InteractiveProcessManager;
