/**
 * Gemini-Style Process Manager for Black-G CLI
 * Manages real command execution with Gemini CLI-style interface
 */

const { spawn } = require('child_process');
const { EventEmitter } = require('events');

class GeminiStyleProcessManager extends EventEmitter {
    constructor(terminal, options = {}) {
        super();
        
        this.terminal = terminal;
        this.options = {
            maxConcurrentProcesses: options.maxConcurrentProcesses || 3,
            outputBufferSize: options.outputBufferSize || 1024 * 1024,
            progressUpdateInterval: options.progressUpdateInterval || 500,
            ...options
        };

        this.activeProcesses = new Map();
        this.processHistory = [];
        this.processCounter = 0;
        
        this.setupEventHandlers();
    }

    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Handle terminal input
        this.terminal.on('input', (input) => this.handleInput(input));
        this.terminal.on('interrupt', () => this.handleInterrupt());
    }

    /**
     * Handle user input
     */
    async handleInput(input) {
        if (!input) return;

        // Handle built-in commands
        if (await this.handleBuiltinCommands(input)) {
            return;
        }

        // Process as AI request or direct command
        if (this.isDirectCommand(input)) {
            await this.executeDirectCommand(input);
        } else {
            await this.processAIRequest(input);
        }
    }

    /**
     * Handle built-in commands
     */
    async handleBuiltinCommands(input) {
        const command = input.toLowerCase().trim();
        
        switch (command) {
            case 'help':
                this.showHelp();
                return true;
                
            case 'status':
                this.showStatus();
                return true;
                
            case 'processes':
                this.showProcesses();
                return true;
                
            case 'clear':
                this.terminal.redrawInterface();
                return true;
                
            case 'demo':
                this.runDemo();
                return true;
                
            default:
                return false;
        }
    }

    /**
     * Check if input is a direct command
     */
    isDirectCommand(input) {
        const directCommands = ['nmap', 'masscan', 'subfinder', 'amass', 'nuclei', 'gobuster', 'sslscan', 'dirb', 'nikto', 'whatweb'];
        const firstWord = input.split(' ')[0].toLowerCase();
        return directCommands.includes(firstWord);
    }

    /**
     * Execute direct command
     */
    async executeDirectCommand(command) {
        this.terminal.showMessage(`Executing: ${command}`, 'info');
        
        // Show permission dialog
        const confirmed = await this.showPermissionDialog(command);
        if (!confirmed) {
            this.terminal.showMessage('Command execution cancelled', 'warning');
            return;
        }

        await this.executeCommand(command);
    }

    /**
     * Process AI request
     */
    async processAIRequest(input) {
        this.terminal.showMessage('Processing AI request...', 'info');
        
        // Simulate AI processing
        await this.sleep(1000);
        
        // Generate suggested commands based on input
        const commands = this.generateCommandsFromAI(input);
        
        if (commands.length === 0) {
            this.terminal.showMessage('No commands suggested by AI', 'warning');
            return;
        }

        // Show AI analysis
        this.showAIAnalysis(input, commands);
        
        // Execute suggested commands with permission
        for (const cmd of commands) {
            const confirmed = await this.showPermissionDialog(cmd.command, cmd.description, cmd.risk);
            if (confirmed) {
                await this.executeCommand(cmd.command, cmd.description);
            }
        }
    }

    /**
     * Generate commands from AI input (simplified simulation)
     */
    generateCommandsFromAI(input) {
        const commands = [];
        const lowerInput = input.toLowerCase();
        
        if (lowerInput.includes('subdomain') || lowerInput.includes('find domains')) {
            const domain = this.extractDomain(input) || 'example.com';
            commands.push({
                command: `subfinder -d ${domain}`,
                description: 'Passive subdomain discovery',
                risk: 'low'
            });
            commands.push({
                command: `amass enum -d ${domain}`,
                description: 'Advanced subdomain enumeration',
                risk: 'low'
            });
        }
        
        if (lowerInput.includes('scan') || lowerInput.includes('port')) {
            const target = this.extractDomain(input) || 'example.com';
            commands.push({
                command: `nmap -sS -T4 ${target}`,
                description: 'TCP SYN port scan',
                risk: 'high'
            });
        }
        
        if (lowerInput.includes('vulnerability') || lowerInput.includes('vuln')) {
            const target = this.extractDomain(input) || 'example.com';
            commands.push({
                command: `nuclei -u https://${target}`,
                description: 'Vulnerability scanning with nuclei',
                risk: 'medium'
            });
        }
        
        if (lowerInput.includes('directory') || lowerInput.includes('web')) {
            const target = this.extractDomain(input) || 'example.com';
            commands.push({
                command: `gobuster dir -u https://${target} -w /usr/share/wordlists/dirb/common.txt`,
                description: 'Directory brute-forcing',
                risk: 'medium'
            });
        }
        
        return commands;
    }

    /**
     * Extract domain from input
     */
    extractDomain(input) {
        const domainRegex = /(?:https?:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
        const match = input.match(domainRegex);
        return match ? match[1] : null;
    }

    /**
     * Show AI analysis
     */
    showAIAnalysis(input, commands) {
        this.terminal.showMessage('AI Analysis Complete', 'success');
        console.log(`\n${this.terminal.colors.brightCyan}🤖 AI ANALYSIS:${this.terminal.colors.reset}`);
        console.log(`Request: "${input}"`);
        console.log(`Suggested ${commands.length} command(s):`);
        
        commands.forEach((cmd, index) => {
            const riskColor = cmd.risk === 'high' ? this.terminal.colors.brightRed : 
                             cmd.risk === 'medium' ? this.terminal.colors.brightYellow : 
                             this.terminal.colors.brightGreen;
            console.log(`  ${index + 1}. ${cmd.command}`);
            console.log(`     ${cmd.description} ${riskColor}[${cmd.risk.toUpperCase()} RISK]${this.terminal.colors.reset}`);
        });
        console.log('');
    }

    /**
     * Show permission dialog
     */
    async showPermissionDialog(command, description = '', risk = 'medium') {
        const riskColor = risk === 'high' ? this.terminal.colors.brightRed : 
                         risk === 'medium' ? this.terminal.colors.brightYellow : 
                         this.terminal.colors.brightGreen;
        
        console.log(`\n${this.terminal.colors.white}┌${'─'.repeat(80)}┐${this.terminal.colors.reset}`);
        console.log(`${this.terminal.colors.white}│ ${this.terminal.colors.brightCyan}COMMAND EXECUTION PERMISSION${this.terminal.colors.reset}${' '.repeat(51)}${this.terminal.colors.white}│${this.terminal.colors.reset}`);
        console.log(`${this.terminal.colors.white}├${'─'.repeat(80)}┤${this.terminal.colors.reset}`);
        console.log(`${this.terminal.colors.white}│ Command: ${this.terminal.colors.brightWhite}${command}${this.terminal.colors.reset}${' '.repeat(Math.max(0, 69 - command.length))}${this.terminal.colors.white}│${this.terminal.colors.reset}`);
        if (description) {
            console.log(`${this.terminal.colors.white}│ Description: ${description}${' '.repeat(Math.max(0, 67 - description.length))}${this.terminal.colors.white}│${this.terminal.colors.reset}`);
        }
        console.log(`${this.terminal.colors.white}│ Risk Level: ${riskColor}${risk.toUpperCase()}${this.terminal.colors.reset}${' '.repeat(Math.max(0, 69 - risk.length))}${this.terminal.colors.white}│${this.terminal.colors.reset}`);
        console.log(`${this.terminal.colors.white}└${'─'.repeat(80)}┘${this.terminal.colors.reset}`);
        
        // For demo purposes, auto-approve after 2 seconds
        console.log(`${this.terminal.colors.brightYellow}Auto-approving in 2 seconds... (Press Ctrl+C to cancel)${this.terminal.colors.reset}`);
        await this.sleep(2000);
        
        return true;
    }

    /**
     * Execute command with Gemini-style interface
     */
    async executeCommand(command, description = '') {
        const processId = this.generateProcessId();
        const boxId = this.terminal.createCommandBox(command, description);
        
        const processInfo = {
            id: processId,
            boxId: boxId,
            command: command,
            description: description,
            startTime: Date.now(),
            status: 'running'
        };
        
        this.activeProcesses.set(processId, processInfo);
        
        return new Promise((resolve, reject) => {
            const parts = command.split(' ');
            const tool = parts[0];
            const args = parts.slice(1);
            
            let output = '';
            let progress = 0;
            
            // Start the actual process
            const child = spawn(tool, args, {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: process.env
            });
            
            processInfo.child = child;
            processInfo.pid = child.pid;
            
            // Progress simulation
            const progressInterval = setInterval(() => {
                progress += Math.random() * 5;
                if (progress > 95) progress = 95; // Don't complete until process ends
                
                this.terminal.updateCommandBox(boxId, null, 'running', progress);
            }, this.options.progressUpdateInterval);
            
            // Handle stdout
            child.stdout.on('data', (data) => {
                const chunk = data.toString();
                output += chunk;
                
                // Update terminal with new output
                this.terminal.updateCommandBox(boxId, chunk, 'running', progress);
            });
            
            // Handle stderr
            child.stderr.on('data', (data) => {
                const chunk = data.toString();
                output += chunk;
                
                // Update terminal with error output
                this.terminal.updateCommandBox(boxId, `ERROR: ${chunk}`, 'running', progress);
            });
            
            // Handle process completion
            child.on('close', (code) => {
                clearInterval(progressInterval);
                
                processInfo.exitCode = code;
                processInfo.endTime = Date.now();
                processInfo.duration = processInfo.endTime - processInfo.startTime;
                processInfo.status = code === 0 ? 'completed' : 'failed';
                
                // Complete the terminal box
                this.terminal.completeCommandBox(boxId, code);
                
                // Move to history
                this.processHistory.push(processInfo);
                this.activeProcesses.delete(processId);
                
                if (code === 0) {
                    resolve({
                        success: true,
                        output: output,
                        duration: processInfo.duration
                    });
                } else {
                    reject(new Error(`Command failed with exit code ${code}`));
                }
            });
            
            // Handle process errors
            child.on('error', (err) => {
                clearInterval(progressInterval);
                
                processInfo.status = 'failed';
                processInfo.error = err.message;
                
                this.terminal.completeCommandBox(boxId, 1);
                this.activeProcesses.delete(processId);
                
                reject(new Error(`Failed to execute command: ${err.message}`));
            });
        });
    }

    /**
     * Show help
     */
    showHelp() {
        this.terminal.showMessage('Available commands:', 'info');
        console.log(`
${this.terminal.colors.brightCyan}Built-in Commands:${this.terminal.colors.reset}
  help                 Show this help
  status               Show system status
  processes            Show active processes
  clear                Clear screen
  demo                 Run demo commands

${this.terminal.colors.brightGreen}AI Commands (natural language):${this.terminal.colors.reset}
  "Find subdomains for example.com"
  "Scan example.com for open ports"
  "Check vulnerabilities on target.com"
  "Directory enumeration on website.com"

${this.terminal.colors.brightYellow}Direct Commands:${this.terminal.colors.reset}
  nmap -sS example.com
  subfinder -d example.com
  nuclei -u https://example.com
`);
    }

    /**
     * Show status
     */
    showStatus() {
        this.terminal.showMessage(`Active processes: ${this.activeProcesses.size}`, 'info');
        this.terminal.showMessage(`Completed processes: ${this.processHistory.length}`, 'info');
    }

    /**
     * Show processes
     */
    showProcesses() {
        if (this.activeProcesses.size === 0) {
            this.terminal.showMessage('No active processes', 'info');
            return;
        }
        
        console.log(`\n${this.terminal.colors.brightCyan}Active Processes:${this.terminal.colors.reset}`);
        for (const process of this.activeProcesses.values()) {
            const duration = Date.now() - process.startTime;
            console.log(`  ${process.id}: ${process.command} (${Math.round(duration/1000)}s)`);
        }
    }

    /**
     * Run demo
     */
    runDemo() {
        this.terminal.showMessage('Running demo commands...', 'info');
        
        // Demo commands
        setTimeout(() => this.terminal.simulateCommand('subfinder -d example.com'), 500);
        setTimeout(() => this.terminal.simulateCommand('nmap -sS example.com'), 2000);
        setTimeout(() => this.terminal.simulateCommand('nuclei -u https://example.com'), 4000);
    }

    /**
     * Handle interrupt
     */
    handleInterrupt() {
        if (this.activeProcesses.size > 0) {
            this.terminal.showMessage('Terminating active processes...', 'warning');
            for (const process of this.activeProcesses.values()) {
                if (process.child) {
                    process.child.kill('SIGTERM');
                }
            }
            this.activeProcesses.clear();
        }
    }

    /**
     * Generate process ID
     */
    generateProcessId() {
        return `proc_${Date.now()}_${++this.processCounter}`;
    }

    /**
     * Sleep utility
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = GeminiStyleProcessManager;
