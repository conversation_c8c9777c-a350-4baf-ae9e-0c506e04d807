#!/usr/bin/env node

/**
 * Enhanced Black-G CLI - Interactive AI-Driven Penetration Testing System
 * Version 2.1 - With Enhanced Process Visibility and Permission Controls
 * 
 * Features:
 * - Real-time process monitoring and control
 * - Interactive terminal interface similar to Gemini CLI
 * - Enhanced permission control with risk assessment
 * - Improved tool discovery for Parrot OS
 * - Visible terminal output with progress indicators
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

// Import enhanced components
const InteractiveProcessManager = require('./src/core/InteractiveProcessManager');
const EnhancedTerminalInterface = require('./src/core/EnhancedTerminalInterface');
const ParrotOSToolManager = require('./src/core/ParrotOSToolManager');
const EnhancedLogger = require('./src/utils/enhanced-logger');

class EnhancedBlackGCLI {
    constructor() {
        this.genAI = null;
        this.model = null;
        this.chatHistory = [];
        this.currentSession = null;
        
        // Initialize enhanced components
        this.processManager = new InteractiveProcessManager({
            enableRealTimeOutput: true,
            enableProcessControl: true,
            maxConcurrentProcesses: 3
        });
        
        this.terminal = new EnhancedTerminalInterface({
            prompt: 'Black-G> ',
            enableColors: true,
            enableProgressBars: true,
            autoComplete: true
        });
        
        this.toolManager = new ParrotOSToolManager({
            enableAutoInstall: false, // Security: require manual installation
            enablePathFix: true
        });
        
        this.logger = new EnhancedLogger({
            logLevel: process.env.BLACK_G_LOG_LEVEL || 'info',
            enableMetrics: true,
            enableAlerts: true
        });

        this.setupEventHandlers();
        this.initializeSystem();
    }

    /**
     * Setup event handlers for component communication
     */
    setupEventHandlers() {
        // Terminal interface events
        this.terminal.on('input', (input) => this.handleUserInput(input));
        this.terminal.on('interrupt', () => this.handleInterrupt());
        this.terminal.on('statusRequest', () => this.showSystemStatus());
        this.terminal.on('processListRequest', () => this.showActiveProcesses());

        // Process manager events
        this.processManager.on('processStarted', (processInfo) => {
            this.terminal.showStatusLine(processInfo.id, `Started: ${processInfo.command}`, 'info');
        });
        
        this.processManager.on('processCompleted', (processInfo) => {
            this.terminal.showStatusLine(processInfo.id, `Completed: ${processInfo.command}`, 'success');
        });
        
        this.processManager.on('processFailed', (processInfo) => {
            this.terminal.showStatusLine(processInfo.id, `Failed: ${processInfo.command}`, 'error');
        });

        // Tool manager events
        this.toolManager.on('initialized', (stats) => {
            this.terminal.print(`Tool discovery complete: ${stats.availableTools}/${stats.availableTools + stats.missingTools} tools available`, 'success');
        });

        // Logger events
        this.logger.on('alert', (alert) => {
            this.terminal.print(`System Alert: ${alert.type} - ${JSON.stringify(alert.details)}`, 'warning');
        });
    }

    /**
     * Initialize the enhanced system
     */
    async initializeSystem() {
        try {
            this.terminal.print('Initializing Enhanced Black-G CLI...', 'info');
            
            // Initialize AI
            await this.initializeAI();
            
            // Initialize tool manager
            await this.toolManager.initialize();
            
            // Setup chat
            this.initializeChat();
            
            this.terminal.print('Enhanced Black-G CLI ready!', 'success');
            this.terminal.prompt();
            
        } catch (error) {
            this.terminal.print(`Initialization failed: ${error.message}`, 'error');
            process.exit(1);
        }
    }

    /**
     * Initialize AI with enhanced system prompt
     */
    async initializeAI() {
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            throw new Error('GEMINI_API_KEY environment variable not set');
        }
        
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
        
        this.terminal.print('AI system initialized', 'success');
    }

    /**
     * Initialize chat with enhanced system prompt
     */
    initializeChat() {
        const toolStats = this.toolManager.getStatistics();
        const availableTools = this.toolManager.getAvailableTools();
        
        const systemPrompt = `You are Black-G, an advanced AI penetration testing assistant for Parrot OS Security Edition.

ENHANCED CAPABILITIES:
- Interactive process monitoring with real-time output
- Risk assessment for security tool execution
- User permission control for all commands
- Tool availability: ${toolStats.available}/${toolStats.total} tools (${toolStats.availabilityRate}%)

AVAILABLE TOOLS: ${availableTools.join(', ')}

SECURITY PROTOCOLS:
1. Always assess command risk before execution
2. Require user confirmation for all security tools
3. Provide clear warnings for high-risk operations
4. Show estimated execution time and target information
5. Allow user to monitor and control processes

RESPONSE FORMAT:
- Provide clear analysis of the security testing request
- Suggest appropriate tools and commands
- Include risk assessment and warnings
- Format commands clearly for execution approval

Remember: You are assisting with authorized penetration testing and Attack Surface Management. Always emphasize the importance of proper authorization and responsible disclosure.`;

        this.chatHistory = [
            {
                role: 'user',
                parts: [{ text: systemPrompt }]
            },
            {
                role: 'model',
                parts: [{ text: 'Enhanced Black-G AI Assistant initialized. I can help you with interactive penetration testing and Attack Surface Management with full process visibility and control. What security testing objective would you like to pursue?' }]
            }
        ];
    }

    /**
     * Handle user input with enhanced processing
     */
    async handleUserInput(input) {
        if (!input) {
            this.terminal.prompt();
            return;
        }

        try {
            // Log user interaction
            this.logger.info('User input received', { input, timestamp: new Date().toISOString() });
            
            // Process through AI
            await this.processSecurityRequest(input);
            
        } catch (error) {
            this.terminal.print(`Error processing request: ${error.message}`, 'error');
            this.logger.error('Input processing failed', { input, error: error.message });
        }
        
        this.terminal.prompt();
    }

    /**
     * Process security request through AI with enhanced features
     */
    async processSecurityRequest(userInput) {
        const startTime = Date.now();
        
        this.terminal.print('Processing your security testing request...', 'process');
        
        // Add user input to chat history
        this.chatHistory.push({
            role: 'user',
            parts: [{ text: userInput }]
        });

        try {
            // Get AI analysis
            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(userInput);
            const response = await result.response;
            const aiResponse = response.text();

            // Add AI response to chat history
            this.chatHistory.push({
                role: 'model',
                parts: [{ text: aiResponse }]
            });

            // Display AI response
            this.displayAIResponse(aiResponse);
            
            // Extract and handle commands with enhanced process management
            await this.handleAICommands(aiResponse, userInput);
            
            // Log AI interaction
            const duration = Date.now() - startTime;
            this.logger.logAIInteraction(userInput, aiResponse, duration);
            
        } catch (error) {
            this.terminal.print(`AI processing failed: ${error.message}`, 'error');
            this.logger.error('AI processing failed', { userInput, error: error.message });
        }
    }

    /**
     * Display AI response with enhanced formatting
     */
    displayAIResponse(response) {
        console.log('\n' + '═'.repeat(80));
        console.log('🤖 BLACK-G AI ANALYSIS');
        console.log('═'.repeat(80));
        
        // Parse and format the response
        const lines = response.split('\n');
        let inCodeBlock = false;
        
        lines.forEach(line => {
            if (line.startsWith('```')) {
                inCodeBlock = !inCodeBlock;
                return;
            }
            
            if (inCodeBlock) {
                console.log(`\x1b[36m${line}\x1b[0m`); // Cyan for code
            } else if (line.startsWith('**') && line.endsWith('**')) {
                console.log(`\x1b[1m${line.slice(2, -2)}\x1b[0m`); // Bold for headers
            } else if (line.startsWith('- ')) {
                console.log(`\x1b[33m${line}\x1b[0m`); // Yellow for bullet points
            } else {
                console.log(line);
            }
        });
        
        console.log('═'.repeat(80));
    }

    /**
     * Handle AI commands with enhanced process management
     */
    async handleAICommands(aiResponse, originalInput) {
        const commands = this.extractCommands(aiResponse);
        
        if (commands.length === 0) {
            return;
        }

        console.log(`\n🔍 Found ${commands.length} command(s) to execute:`);
        commands.forEach((cmd, index) => {
            console.log(`${index + 1}. ${cmd.command}`);
            if (cmd.description) {
                console.log(`   📝 ${cmd.description}`);
            }
        });

        // Execute commands using enhanced process manager
        for (const cmd of commands) {
            try {
                // Check if tool is available
                const toolName = cmd.command.split(' ')[0];
                if (!this.toolManager.isToolAvailable(toolName)) {
                    this.terminal.print(`Tool '${toolName}' is not available. Please install it first.`, 'error');
                    this.suggestToolInstallation(toolName);
                    continue;
                }

                // Execute with interactive process manager
                const result = await this.processManager.executeInteractiveCommand(cmd.command, {
                    requireConfirmation: true,
                    showRealTimeOutput: true,
                    allowUserControl: true,
                    riskLevel: this.assessCommandRisk(cmd.command),
                    description: cmd.description
                });

                if (result.cancelled) {
                    this.terminal.print('Command execution cancelled by user', 'warning');
                    break;
                }

                if (result.success) {
                    this.terminal.print(`Command completed successfully`, 'success');
                } else {
                    this.terminal.print(`Command failed: ${result.error}`, 'error');
                }

            } catch (error) {
                this.terminal.print(`Command execution failed: ${error.message}`, 'error');
                this.logger.error('Command execution failed', { 
                    command: cmd.command, 
                    error: error.message 
                });
            }
        }
    }

    /**
     * Extract commands from AI response
     */
    extractCommands(response) {
        const commands = [];
        const lines = response.split('\n');
        let currentCommand = null;
        let inCodeBlock = false;

        for (const line of lines) {
            const trimmedLine = line.trim();
            
            if (trimmedLine.startsWith('```')) {
                inCodeBlock = !inCodeBlock;
                continue;
            }

            if (inCodeBlock && trimmedLine) {
                // This is a command in a code block
                if (currentCommand) {
                    commands.push(currentCommand);
                }
                currentCommand = {
                    command: trimmedLine,
                    description: '',
                    category: 'security_tool'
                };
            } else if (currentCommand && trimmedLine.startsWith('Description:')) {
                currentCommand.description = trimmedLine.replace('Description:', '').trim();
            }
        }

        if (currentCommand) {
            commands.push(currentCommand);
        }

        return commands;
    }

    /**
     * Assess command risk level
     */
    assessCommandRisk(command) {
        const cmd = command.toLowerCase();
        
        if (cmd.includes('nmap') && (cmd.includes('-sS') || cmd.includes('-A'))) {
            return 'high';
        } else if (cmd.includes('masscan') || cmd.includes('nuclei')) {
            return 'high';
        } else if (cmd.includes('gobuster') || cmd.includes('dirb')) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Suggest tool installation
     */
    suggestToolInstallation(toolName) {
        const toolInfo = this.toolManager.toolDefinitions[toolName];
        if (toolInfo) {
            console.log(`\n💡 To install ${toolName}:`);
            console.log(`📋 ${toolInfo.installCommand}`);
            console.log(`📝 ${toolInfo.description}`);
        }
    }

    /**
     * Show system status
     */
    showSystemStatus() {
        const toolStats = this.toolManager.getStatistics();
        const activeProcesses = this.processManager.getActiveProcesses();
        const metrics = this.logger.getMetrics();

        console.log('\n📊 SYSTEM STATUS');
        console.log('═'.repeat(50));
        console.log(`🔧 Tools Available: ${toolStats.available}/${toolStats.total} (${toolStats.availabilityRate}%)`);
        console.log(`🔄 Active Processes: ${activeProcesses.length}`);
        console.log(`📈 Success Rate: ${metrics.successRate}%`);
        console.log(`⏱️  Average Response Time: ${metrics.averageResponseTime}ms`);
        console.log(`🕐 Uptime: ${Math.round(metrics.uptime / 1000)}s`);
    }

    /**
     * Show active processes
     */
    showActiveProcesses() {
        const processes = this.processManager.getActiveProcesses();
        
        if (processes.length === 0) {
            this.terminal.print('No active processes', 'info');
            return;
        }

        console.log('\n🔄 ACTIVE PROCESSES');
        console.log('═'.repeat(60));
        
        processes.forEach(proc => {
            const duration = Math.round(proc.duration / 1000);
            console.log(`📋 ${proc.id}: ${proc.command}`);
            console.log(`   Status: ${proc.status} | Duration: ${duration}s | PID: ${proc.pid}`);
        });
    }

    /**
     * Handle interrupt signal
     */
    handleInterrupt() {
        const activeProcesses = this.processManager.getActiveProcesses();
        
        if (activeProcesses.length > 0) {
            this.terminal.print('Terminating active processes...', 'warning');
            this.processManager.killAllProcesses();
        } else {
            this.terminal.print('No active processes to terminate', 'info');
        }
    }
}

// Main execution
if (require.main === module) {
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down Enhanced Black-G CLI...');
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\n🛑 Received termination signal...');
        process.exit(0);
    });

    // Start Enhanced Black-G CLI
    try {
        new EnhancedBlackGCLI();
    } catch (error) {
        console.error(`❌ Failed to start Enhanced Black-G CLI: ${error.message}`);
        process.exit(1);
    }
}

module.exports = EnhancedBlackGCLI;
