#!/bin/bash

# Gemini-Style Black-G CLI Startup Script
# Launches the Gemini CLI-style interface

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check essential requirements
check_essentials() {
    print_header "🚀 Gemini-Style CLI Quick Check..."
    
    local all_good=true
    
    # Check Node.js
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version)
        print_success "Node.js: $node_version"
    else
        print_error "Node.js is not installed"
        all_good=false
    fi
    
    # Check .env file
    if [ -f "$SCRIPT_DIR/.env" ]; then
        if grep -q "GEMINI_API_KEY=" "$SCRIPT_DIR/.env"; then
            local api_key=$(grep "GEMINI_API_KEY=" "$SCRIPT_DIR/.env" | cut -d'=' -f2)
            if [ -n "$api_key" ] && [ "$api_key" != "your_api_key_here" ]; then
                print_success "Gemini API key configured"
            else
                print_error "Gemini API key not properly configured"
                print_status "Please set GEMINI_API_KEY in .env file"
                all_good=false
            fi
        else
            print_error "GEMINI_API_KEY not found in .env file"
            all_good=false
        fi
    else
        print_error ".env file not found"
        all_good=false
    fi
    
    # Check dependencies
    if [ -d "$SCRIPT_DIR/node_modules" ]; then
        print_success "Dependencies installed"
    else
        print_warning "Installing dependencies..."
        cd "$SCRIPT_DIR"
        npm install --silent
        print_success "Dependencies installed"
    fi
    
    if [ "$all_good" = false ]; then
        print_error "Some requirements are missing. Please fix them before continuing."
        exit 1
    fi
    
    print_success "Ready to launch Gemini-style interface!"
}

# Function to start the Gemini-style CLI
start_gemini_cli() {
    print_header "🚀 Starting Gemini-Style Black-G CLI..."
    
    cd "$SCRIPT_DIR"
    
    # Make sure the CLI is executable
    chmod +x gemini-style-black-g.js 2>/dev/null || true
    
    # Clear screen for clean start
    clear
    
    # Show startup banner
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════════════╗"
    echo "║                    Gemini-Style Black-G CLI v2.0                    ║"
    echo "║                  Interactive AI Penetration Testing                 ║"
    echo "║                     Real-time Command Execution                     ║"
    echo "╚══════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    print_status "Launching Gemini-style interface..."
    echo ""
    
    # Start the Gemini-style CLI
    node gemini-style-black-g.js
}

# Function to show usage
show_usage() {
    echo "Gemini-Style Black-G CLI Startup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --check-only    Only run essential checks, don't start CLI"
    echo "  --no-checks     Skip checks and start directly"
    echo "  --demo          Start with demo mode"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run checks and start Gemini-style CLI"
    echo "  $0 --check-only # Only run essential checks"
    echo "  $0 --no-checks  # Start CLI without checks"
    echo "  $0 --demo       # Start with demo commands"
}

# Function to run demo
run_demo() {
    print_header "🎬 Starting Gemini-Style CLI Demo..."
    
    # Set demo environment variable
    export BLACK_G_DEMO_MODE=true
    
    start_gemini_cli
}

# Function to show system info
show_system_info() {
    print_header "📊 System Information"
    
    echo "Node.js: $(node --version 2>/dev/null || echo 'Not installed')"
    echo "npm: v$(npm --version 2>/dev/null || echo 'Not installed')"
    echo "OS: $(uname -s) $(uname -r)"
    echo "Terminal: $TERM"
    echo "Shell: $SHELL"
    echo "Working Directory: $(pwd)"
    
    # Check security tools
    echo ""
    echo "Security Tools:"
    local tools=("nmap" "masscan" "subfinder" "amass" "nuclei" "gobuster" "sslscan")
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            echo "  ✓ $tool"
        else
            echo "  ✗ $tool"
        fi
    done
}

# Main execution
main() {
    # Parse command line arguments
    case "${1:-}" in
        --help)
            show_usage
            exit 0
            ;;
        --check-only)
            check_essentials
            print_success "Essential checks completed successfully"
            exit 0
            ;;
        --no-checks)
            print_status "Skipping checks and starting Gemini-style CLI directly..."
            start_gemini_cli
            ;;
        --demo)
            check_essentials
            run_demo
            ;;
        --info)
            show_system_info
            exit 0
            ;;
        "")
            # Default behavior
            check_essentials
            start_gemini_cli
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Trap Ctrl+C
trap 'print_status "Shutting down..."; exit 0' INT

# Show banner
echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════════════╗"
echo "║                  Gemini-Style Black-G CLI Launcher                  ║"
echo "║                   Real-time Command Execution                       ║"
echo "╚══════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Run main function
main "$@"
