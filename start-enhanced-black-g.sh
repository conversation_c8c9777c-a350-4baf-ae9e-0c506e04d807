#!/bin/bash

# Enhanced Black-G CLI Startup Script
# Provides easy startup with system checks and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check if command exists
command_exists() {
    timeout 5 command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_requirements() {
    print_header "🔍 Checking System Requirements..."
    
    local all_good=true
    
    # Check Node.js
    if command_exists node; then
        local node_version=$(node --version)
        print_success "Node.js: $node_version"
    else
        print_error "Node.js is not installed"
        all_good=false
    fi
    
    # Check npm
    if command_exists npm; then
        local npm_version=$(npm --version)
        print_success "npm: v$npm_version"
    else
        print_error "npm is not installed"
        all_good=false
    fi
    
    # Check if we're on Parrot OS
    if [ -f /etc/os-release ]; then
        if grep -q -i "parrot" /etc/os-release; then
            print_success "Running on Parrot OS"
        else
            print_warning "Not running on Parrot OS - some features may not work optimally"
        fi
    fi
    
    # Check Go installation
    if command_exists go; then
        local go_version=$(go version | awk '{print $3}')
        print_success "Go: $go_version"
    else
        print_warning "Go is not installed - some security tools may not be available"
    fi
    
    # Check for security tools
    print_status "Checking security tools availability..."

    local tools=("nmap" "masscan" "sslscan" "dirb" "nikto" "whatweb")
    local available_tools=0

    for tool in "${tools[@]}"; do
        if timeout 3 command -v "$tool" >/dev/null 2>&1; then
            print_success "  ✓ $tool"
            ((available_tools++))
        else
            print_warning "  ✗ $tool (not found)"
        fi
    done

    print_status "Security tools available: $available_tools/${#tools[@]}"

    # Check Go-based tools
    local go_tools=("subfinder" "amass" "nuclei" "gobuster")
    local go_available=0

    for tool in "${go_tools[@]}"; do
        if timeout 3 command -v "$tool" >/dev/null 2>&1; then
            print_success "  ✓ $tool (Go-based)"
            ((go_available++))
        else
            print_warning "  ✗ $tool (Go-based, not found)"
        fi
    done

    print_status "Go-based tools available: $go_available/${#go_tools[@]}"
    
    if [ "$all_good" = false ]; then
        print_error "Some requirements are missing. Please install them before continuing."
        exit 1
    fi
}

# Function to check environment configuration
check_environment() {
    print_header "🔧 Checking Environment Configuration..."
    
    # Check .env file
    if [ -f "$SCRIPT_DIR/.env" ]; then
        print_success ".env file found"
        
        # Check for API key
        if grep -q "GEMINI_API_KEY=" "$SCRIPT_DIR/.env"; then
            local api_key=$(grep "GEMINI_API_KEY=" "$SCRIPT_DIR/.env" | cut -d'=' -f2)
            if [ -n "$api_key" ] && [ "$api_key" != "your_api_key_here" ]; then
                print_success "Gemini API key configured"
            else
                print_error "Gemini API key not properly configured in .env file"
                print_status "Please set GEMINI_API_KEY=your_actual_api_key in .env file"
                exit 1
            fi
        else
            print_error "GEMINI_API_KEY not found in .env file"
            exit 1
        fi
    else
        print_error ".env file not found"
        print_status "Creating .env template..."
        cat > "$SCRIPT_DIR/.env" << EOF
# Enhanced Black-G CLI Configuration
GEMINI_API_KEY=your_api_key_here
BLACK_G_LOG_LEVEL=info
EOF
        print_status "Please edit .env file and set your Gemini API key"
        exit 1
    fi
    
    # Check node_modules
    if [ -d "$SCRIPT_DIR/node_modules" ]; then
        print_success "Node modules installed"
    else
        print_warning "Node modules not found - running npm install..."
        cd "$SCRIPT_DIR"
        npm install
        print_success "Dependencies installed"
    fi
}

# Function to check PATH configuration
check_path() {
    print_header "🛤️  Checking PATH Configuration..."
    
    local paths_to_check=(
        "/usr/bin"
        "/usr/local/bin"
        "/home/<USER>/go/bin"
        "/home/<USER>/.cargo/bin"
        "/home/<USER>/.local/bin"
    )
    
    local missing_paths=()
    
    for path_dir in "${paths_to_check[@]}"; do
        if [ -d "$path_dir" ]; then
            if [[ ":$PATH:" == *":$path_dir:"* ]]; then
                print_success "  ✓ $path_dir (in PATH)"
            else
                print_warning "  ✗ $path_dir (missing from PATH)"
                missing_paths+=("$path_dir")
            fi
        fi
    done
    
    if [ ${#missing_paths[@]} -gt 0 ]; then
        print_warning "Some directories are missing from PATH"
        print_status "To fix this, add the following to your ~/.bashrc or ~/.zshrc:"
        echo "export PATH=\"$(IFS=:; echo "${missing_paths[*]}"):$PATH\""
    fi
}

# Function to run tests
run_tests() {
    print_header "🧪 Running System Tests..."
    
    if [ -f "$SCRIPT_DIR/test-enhanced-black-g.js" ]; then
        cd "$SCRIPT_DIR"
        node test-enhanced-black-g.js
    else
        print_warning "Test file not found - skipping tests"
    fi
}

# Function to start the enhanced CLI
start_cli() {
    print_header "🚀 Starting Enhanced Black-G CLI..."
    
    cd "$SCRIPT_DIR"
    
    # Make sure the CLI is executable
    chmod +x enhanced-black-g-cli.js
    
    # Start the CLI
    node enhanced-black-g-cli.js
}

# Function to show usage
show_usage() {
    echo "Enhanced Black-G CLI Startup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --check-only    Only run system checks, don't start CLI"
    echo "  --test          Run tests before starting"
    echo "  --no-checks     Skip system checks and start directly"
    echo "  --install-deps  Install missing dependencies"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run checks and start CLI"
    echo "  $0 --test            # Run tests then start CLI"
    echo "  $0 --check-only      # Only run system checks"
    echo "  $0 --install-deps    # Install missing dependencies"
}

# Function to install dependencies
install_dependencies() {
    print_header "📦 Installing Dependencies..."
    
    # Update package list
    print_status "Updating package list..."
    sudo apt update
    
    # Install Node.js and npm if not present
    if ! command_exists node; then
        print_status "Installing Node.js and npm..."
        sudo apt install -y nodejs npm
    fi
    
    # Install Go if not present
    if ! command_exists go; then
        print_status "Installing Go..."
        sudo apt install -y golang-go
    fi
    
    # Install security tools
    print_status "Installing security tools..."
    sudo apt install -y nmap masscan sslscan dirb nikto whatweb
    
    # Install Go-based tools
    if command_exists go; then
        print_status "Installing Go-based security tools..."
        
        # Set up Go environment
        export GOPATH="$HOME/go"
        export PATH="$PATH:$GOPATH/bin"
        
        # Install tools
        go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
        go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest
        go install github.com/OJ/gobuster/v3@latest
        
        print_success "Go-based tools installed"
    fi
    
    # Install npm dependencies
    if [ -f "$SCRIPT_DIR/package.json" ]; then
        print_status "Installing npm dependencies..."
        cd "$SCRIPT_DIR"
        npm install
    fi
    
    print_success "All dependencies installed"
}

# Main execution
main() {
    # Parse command line arguments
    case "${1:-}" in
        --help)
            show_usage
            exit 0
            ;;
        --check-only)
            check_requirements
            check_environment
            check_path
            print_success "System checks completed"
            exit 0
            ;;
        --test)
            check_requirements
            check_environment
            run_tests
            start_cli
            ;;
        --no-checks)
            start_cli
            ;;
        --install-deps)
            install_dependencies
            exit 0
            ;;
        "")
            # Default behavior
            check_requirements
            check_environment
            check_path
            start_cli
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Trap Ctrl+C
trap 'print_status "Shutting down..."; exit 0' INT

# Run main function
main "$@"
